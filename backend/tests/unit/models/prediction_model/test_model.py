import json
import os
import tempfile
from unittest.mock import <PERSON><PERSON><PERSON>, Mock, call, patch

import numpy as np
import optuna
import pandas as pd
import pytest
import torch
import torch.nn as nn
from sklearn.preprocessing import StandardScaler

# Import the module under test
from src.models.prediction_model.model import (
    DEVICE,
    FUTURE_PREDICTION_DAYS,
    LOOK_BACK,
    MODEL_VERSION,
    N_OPTUNA_TRIALS,
    QUANTILES,
    AttentionPooling,
    FeatureMaskingLayer,
    LegacyTimeSeriesTransformer,
    PositionalEncoding,
    QuantileLoss,
    TimeSeriesTransformer,
    inverse_transform_predictions,
    load_versioned_model,
    objective,
    plot_predictions,
    predict_future,
    run_training_pipeline,
    save_versioned_model,
)


class TestPositionalEncoding:
    """Test suite for PositionalEncoding class."""

    def test_positional_encoding_initialization(self):
        """Test PositionalEncoding initialization."""
        d_model = 64
        dropout = 0.1
        max_len = 1000

        pos_enc = PositionalEncoding(d_model, dropout, max_len)

        assert pos_enc.dropout.p == dropout
        assert pos_enc.pe.shape == (max_len, 1, d_model)

    def test_positional_encoding_forward(self):
        """Test PositionalEncoding forward pass."""
        d_model = 64
        seq_len = 30
        batch_size = 16

        pos_enc = PositionalEncoding(d_model)
        x = torch.randn(seq_len, batch_size, d_model)

        output = pos_enc(x)

        assert output.shape == (seq_len, batch_size, d_model)
        assert not torch.equal(x, output)  # Should be modified by positional encoding

    def test_positional_encoding_different_sequence_lengths(self):
        """Test PositionalEncoding with different sequence lengths."""
        d_model = 32
        pos_enc = PositionalEncoding(d_model)

        for seq_len in [10, 30, 50]:
            x = torch.randn(seq_len, 4, d_model)
            output = pos_enc(x)
            assert output.shape == (seq_len, 4, d_model)


class TestAttentionPooling:
    """Test suite for AttentionPooling class."""

    def test_attention_pooling_initialization(self):
        """Test AttentionPooling initialization."""
        d_model = 64
        num_heads = 2

        pooling = AttentionPooling(d_model, num_heads)

        assert pooling.attention.embed_dim == d_model
        assert pooling.attention.num_heads == num_heads
        assert pooling.query.shape == (1, 1, d_model)

    def test_attention_pooling_forward_pass(self):
        """Test AttentionPooling forward pass."""
        d_model = 64
        batch_size = 8
        seq_len = 30

        pooling = AttentionPooling(d_model, num_heads=2)
        x = torch.randn(batch_size, seq_len, d_model)

        pooled_output, attention_weights = pooling(x)

        assert pooled_output.shape == (batch_size, d_model)
        assert attention_weights.shape == (batch_size, 1, seq_len)

    def test_attention_pooling_different_batch_sizes(self):
        """Test AttentionPooling with different batch sizes."""
        d_model = 32
        seq_len = 20

        pooling = AttentionPooling(d_model, num_heads=1)

        for batch_size in [1, 4, 16]:
            x = torch.randn(batch_size, seq_len, d_model)
            pooled_output, attention_weights = pooling(x)

            assert pooled_output.shape == (batch_size, d_model)
            assert attention_weights.shape == (batch_size, 1, seq_len)


class TestFeatureMaskingLayer:
    """Test suite for FeatureMaskingLayer class."""

    def test_feature_masking_initialization(self):
        """Test FeatureMaskingLayer initialization."""
        input_dim = 20

        masking = FeatureMaskingLayer(input_dim)

        assert masking.feature_gates.shape == (input_dim,)
        assert isinstance(masking.feature_importance, nn.Linear)
        assert masking.feature_importance.in_features == input_dim
        assert masking.feature_importance.out_features == input_dim

    def test_feature_masking_forward_pass(self):
        """Test FeatureMaskingLayer forward pass."""
        input_dim = 15
        batch_size = 8
        seq_len = 30

        masking = FeatureMaskingLayer(input_dim)
        x = torch.randn(batch_size, seq_len, input_dim)

        output = masking(x)

        assert output.shape == (batch_size, seq_len, input_dim)

    def test_feature_masking_preserves_dimensions(self):
        """Test that feature masking preserves input dimensions."""
        input_dim = 25

        masking = FeatureMaskingLayer(input_dim)

        for batch_size in [1, 4, 16]:
            for seq_len in [10, 30, 50]:
                x = torch.randn(batch_size, seq_len, input_dim)
                output = masking(x)

                assert output.shape == x.shape

    def test_feature_masking_gates_effect(self):
        """Test that feature gates affect the output."""
        input_dim = 10
        batch_size = 4
        seq_len = 20

        masking = FeatureMaskingLayer(input_dim)
        x = torch.randn(batch_size, seq_len, input_dim)

        # Set some gates to zero
        with torch.no_grad():
            masking.feature_gates[0] = 0.0
            masking.feature_gates[1] = 0.0

        output = masking(x)

        # The gated features should be significantly reduced (though not exactly zero due to dynamic weighting)
        assert torch.abs(output[:, :, 0]).mean() < torch.abs(x[:, :, 0]).mean()
        assert torch.abs(output[:, :, 1]).mean() < torch.abs(x[:, :, 1]).mean()


class TestTimeSeriesTransformer:
    """Test suite for TimeSeriesTransformer class."""

    def test_transformer_initialization(self):
        """Test TimeSeriesTransformer initialization."""
        input_dim = 14
        d_model = 64
        nhead = 4
        num_encoder_layers = 2
        dim_feedforward = 256
        dropout = 0.1
        output_dim = 10
        num_quantiles = 3

        model = TimeSeriesTransformer(
            input_dim, d_model, nhead, num_encoder_layers, dim_feedforward, dropout, output_dim, num_quantiles
        )

        assert model.d_model == d_model
        assert model.output_dim == output_dim
        assert model.num_quantiles == num_quantiles
        assert isinstance(model.feature_masking, FeatureMaskingLayer)
        assert isinstance(model.embedding, nn.Linear)
        assert isinstance(model.pos_encoder, PositionalEncoding)
        assert isinstance(model.attention_pooling, AttentionPooling)
        assert isinstance(model.decoder, nn.Linear)

    def test_transformer_forward_pass(self):
        """Test TimeSeriesTransformer forward pass."""
        input_dim = 14
        d_model = 64
        nhead = 4
        batch_size = 8
        seq_len = 30
        output_dim = 10
        num_quantiles = 3

        model = TimeSeriesTransformer(input_dim, d_model, nhead, 2, 256, 0.1, output_dim, num_quantiles)

        x = torch.randn(batch_size, seq_len, input_dim)
        output = model(x)

        assert output.shape == (batch_size, output_dim, num_quantiles)

    def test_transformer_weight_initialization(self):
        """Test TimeSeriesTransformer weight initialization."""
        model = TimeSeriesTransformer(14, 64, 4, 2, 256, 0.1, 10, 3)

        # Check that weights are initialized within expected range
        embedding_weights = model.embedding.weight.data
        decoder_weights = model.decoder.weight.data
        decoder_bias = model.decoder.bias.data

        assert torch.all(torch.abs(embedding_weights) <= 0.1)
        assert torch.all(torch.abs(decoder_weights) <= 0.1)
        assert torch.all(decoder_bias == 0)


class TestBackwardCompatibility:
    """Test suite for backward compatibility functionality."""

    def test_legacy_transformer_initialization(self):
        """Test LegacyTimeSeriesTransformer initialization."""
        input_dim = 14
        d_model = 64
        nhead = 4
        num_encoder_layers = 2
        dim_feedforward = 256
        dropout = 0.1
        output_dim = 10
        num_quantiles = 3

        model = LegacyTimeSeriesTransformer(
            input_dim, d_model, nhead, num_encoder_layers, dim_feedforward, dropout, output_dim, num_quantiles
        )

        assert model.d_model == d_model
        assert model.output_dim == output_dim
        assert model.num_quantiles == num_quantiles
        assert isinstance(model.embedding, nn.Linear)
        assert isinstance(model.pos_encoder, PositionalEncoding)
        assert isinstance(model.decoder, nn.Linear)
        # Legacy model should not have feature masking or attention pooling
        assert not hasattr(model, 'feature_masking')
        assert not hasattr(model, 'attention_pooling')

    def test_legacy_transformer_forward_pass(self):
        """Test LegacyTimeSeriesTransformer forward pass."""
        input_dim = 14
        d_model = 64
        nhead = 4
        batch_size = 8
        seq_len = 30
        output_dim = 10
        num_quantiles = 3

        model = LegacyTimeSeriesTransformer(input_dim, d_model, nhead, 2, 256, 0.1, output_dim, num_quantiles)

        x = torch.randn(batch_size, seq_len, input_dim)
        output = model(x)

        assert output.shape == (batch_size, output_dim, num_quantiles)

    def test_versioned_model_saving_and_loading(self):
        """Test versioned model saving and loading."""
        input_dim = 10
        d_model = 32
        nhead = 2
        batch_size = 4
        seq_len = 20
        output_dim = 5
        num_quantiles = 3

        # Create and test current model
        model = TimeSeriesTransformer(input_dim, d_model, nhead, 2, 128, 0.1, output_dim, num_quantiles).to(DEVICE)

        with tempfile.NamedTemporaryFile(suffix='.pth', delete=False) as tmp_file:
            checkpoint_path = tmp_file.name

        try:
            # Save versioned model
            save_versioned_model(model, checkpoint_path, MODEL_VERSION)

            # Load versioned model
            loaded_model, version = load_versioned_model(
                checkpoint_path, input_dim, d_model, nhead, 2, 128, 0.1, output_dim, num_quantiles, DEVICE
            )

            assert version == MODEL_VERSION
            assert isinstance(loaded_model, TimeSeriesTransformer)

            # Test that loaded model produces same output
            x = torch.randn(batch_size, seq_len, input_dim).to(DEVICE)
            original_output = model(x)
            loaded_output = loaded_model(x)

            assert original_output.shape == loaded_output.shape

        finally:
            os.unlink(checkpoint_path)

    def test_legacy_model_loading(self):
        """Test loading legacy model format."""
        input_dim = 10
        d_model = 32
        nhead = 2
        output_dim = 5
        num_quantiles = 3

        # Create legacy model and save in old format
        legacy_model = LegacyTimeSeriesTransformer(input_dim, d_model, nhead, 2, 128, 0.1, output_dim, num_quantiles).to(DEVICE)

        with tempfile.NamedTemporaryFile(suffix='.pth', delete=False) as tmp_file:
            checkpoint_path = tmp_file.name

        try:
            # Save in legacy format (direct state dict)
            torch.save(legacy_model.state_dict(), checkpoint_path)

            # Load using versioned loader
            loaded_model, version = load_versioned_model(
                checkpoint_path, input_dim, d_model, nhead, 2, 128, 0.1, output_dim, num_quantiles, DEVICE
            )

            assert version == "1.0"
            assert isinstance(loaded_model, LegacyTimeSeriesTransformer)

        finally:
            os.unlink(checkpoint_path)


class TestQuantileLoss:
    """Test suite for QuantileLoss class."""

    def test_quantile_loss_initialization(self):
        """Test QuantileLoss initialization."""
        quantiles = [0.1, 0.5, 0.9]
        loss_fn = QuantileLoss(quantiles)

        assert torch.equal(loss_fn.quantiles, torch.tensor(quantiles, dtype=torch.float32))

    def test_quantile_loss_forward(self):
        """Test QuantileLoss forward pass."""
        quantiles = [0.1, 0.5, 0.9]
        loss_fn = QuantileLoss(quantiles)

        batch_size = 4
        seq_len = 10
        num_quantiles = len(quantiles)

        preds = torch.randn(batch_size, seq_len, num_quantiles)
        target = torch.randn(batch_size, seq_len)

        loss = loss_fn(preds, target)

        assert isinstance(loss, torch.Tensor)
        assert loss.dim() == 0  # Scalar loss
        assert loss.item() >= 0  # Loss should be non-negative

    def test_quantile_loss_device_handling(self):
        """Test QuantileLoss device handling."""
        quantiles = [0.1, 0.5, 0.9]
        loss_fn = QuantileLoss(quantiles)

        # Test with CPU tensors
        preds = torch.randn(2, 5, 3)
        target = torch.randn(2, 5)

        loss = loss_fn(preds, target)
        assert loss.device == preds.device

    def test_quantile_loss_different_quantiles(self):
        """Test QuantileLoss with different quantile configurations."""
        test_cases = [
            [0.5],  # Single quantile (median)
            [0.25, 0.75],  # Two quantiles
            [0.1, 0.3, 0.5, 0.7, 0.9],  # Five quantiles
        ]

        for quantiles in test_cases:
            loss_fn = QuantileLoss(quantiles)
            preds = torch.randn(2, 3, len(quantiles))
            target = torch.randn(2, 3)

            loss = loss_fn(preds, target)
            assert loss.item() >= 0


class TestObjectiveFunction:
    """Test suite for Optuna objective function."""

    @pytest.fixture
    def sample_training_data(self):
        """Create sample training data for testing."""
        batch_size = 32
        seq_len = 30
        input_dim = 14
        output_dim = 10

        X_train = np.random.randn(batch_size, seq_len, input_dim).astype(np.float32)
        y_train = np.random.randn(batch_size, output_dim).astype(np.float32)
        X_eval = np.random.randn(16, seq_len, input_dim).astype(np.float32)
        y_eval = np.random.randn(16, output_dim).astype(np.float32)

        return X_train, y_train, X_eval, y_eval, input_dim

    def test_objective_function_basic(self, sample_training_data):
        """Test basic objective function execution."""
        X_train, y_train, X_eval, y_eval, input_dim = sample_training_data

        # Create a mock trial with fixed hyperparameters
        trial = Mock()
        # Order: lr, d_model, nhead, num_encoder_layers, dim_feedforward, dropout, batch_size, epochs, weight_decay
        trial.suggest_float.side_effect = [0.001, 0.1, 0.0001]  # lr, dropout, weight_decay
        trial.suggest_categorical.side_effect = [64, 4, 256, 32]  # d_model, nhead, dim_feedforward, batch_size
        trial.suggest_int.side_effect = [2, 5]  # num_encoder_layers, epochs
        trial.report = Mock()
        trial.should_prune.return_value = False

        loss = objective(trial, X_train, y_train, X_eval, y_eval, input_dim)

        assert isinstance(loss, float)
        assert loss >= 0
        assert trial.report.called

    def test_objective_function_pruning(self, sample_training_data):
        """Test objective function with pruning."""
        X_train, y_train, X_eval, y_eval, input_dim = sample_training_data

        trial = Mock()
        trial.suggest_float.side_effect = [0.001, 0.1, 0.0001]  # lr, dropout, weight_decay
        trial.suggest_categorical.side_effect = [64, 4, 256, 32]  # d_model, nhead, dim_feedforward, batch_size
        trial.suggest_int.side_effect = [2, 5]  # num_encoder_layers, epochs
        trial.report = Mock()
        trial.should_prune.return_value = True  # Trigger pruning

        with pytest.raises(optuna.exceptions.TrialPruned):
            objective(trial, X_train, y_train, X_eval, y_eval, input_dim)

    def test_objective_function_invalid_d_model_nhead(self, sample_training_data):
        """Test objective function with invalid d_model/nhead combination."""
        X_train, y_train, X_eval, y_eval, input_dim = sample_training_data

        trial = Mock()
        trial.suggest_float.side_effect = [0.001, 0.1, 0.0001]  # lr, dropout, weight_decay
        trial.suggest_categorical.side_effect = [65, 4, 256, 32]  # d_model=65 not divisible by nhead=4
        trial.suggest_int.side_effect = [2, 5]  # num_encoder_layers, epochs

        with pytest.raises(optuna.exceptions.TrialPruned):
            objective(trial, X_train, y_train, X_eval, y_eval, input_dim)


class TestPredictFuture:
    """Test suite for predict_future function."""

    @pytest.fixture
    def sample_model_and_data(self):
        """Create sample model and data for prediction testing."""
        input_dim = 14
        model = TimeSeriesTransformer(input_dim, 64, 4, 2, 256, 0.1, 10, 3)
        model.eval()

        # Move model to CPU to avoid device mismatch issues in tests
        model = model.to("cpu")

        # Create sample scaler
        scaler = StandardScaler()
        sample_data = np.random.randn(100, input_dim)
        scaler.fit(sample_data)

        # Create last sequence
        last_sequence = np.random.randn(LOOK_BACK, input_dim).astype(np.float32)

        return model, scaler, last_sequence, input_dim

    @patch("src.models.prediction_model.model.DEVICE", "cpu")
    def test_predict_future_basic(self, sample_model_and_data):
        """Test basic predict_future functionality."""
        model, scaler, last_sequence, n_features = sample_model_and_data

        returns, prices = predict_future(
            ticker="AAPL",
            model=model,
            last_sequence_scaled=last_sequence,
            scaler=scaler,
            n_features=n_features,
            close_col_idx=3,  # Assuming Close is at index 3
            target_col_idx=13,  # Assuming target return is at index 13
            look_back=LOOK_BACK,
            future_steps=FUTURE_PREDICTION_DAYS,
            bias_correction=0.0,
        )

        assert isinstance(returns, np.ndarray)
        assert isinstance(prices, np.ndarray)
        assert returns.shape == (FUTURE_PREDICTION_DAYS, len(QUANTILES))
        assert prices.shape == (FUTURE_PREDICTION_DAYS, len(QUANTILES))

    @patch("src.models.prediction_model.model.DEVICE", "cpu")
    def test_predict_future_with_bias_correction(self, sample_model_and_data):
        """Test predict_future with bias correction."""
        model, scaler, last_sequence, n_features = sample_model_and_data
        bias_correction = 5.0

        returns, prices = predict_future(
            ticker="AAPL",
            model=model,
            last_sequence_scaled=last_sequence,
            scaler=scaler,
            n_features=n_features,
            close_col_idx=3,
            target_col_idx=13,
            look_back=LOOK_BACK,
            future_steps=FUTURE_PREDICTION_DAYS,
            bias_correction=bias_correction,
        )

        # Prices should be affected by bias correction
        assert isinstance(prices, np.ndarray)
        assert prices.shape == (FUTURE_PREDICTION_DAYS, len(QUANTILES))

    @patch("src.models.prediction_model.model.DEVICE", "cpu")
    def test_predict_future_tensor_input(self, sample_model_and_data):
        """Test predict_future with tensor input."""
        model, scaler, last_sequence, n_features = sample_model_and_data

        # Convert to tensor
        last_sequence_tensor = torch.tensor(last_sequence, dtype=torch.float32)

        returns, prices = predict_future(
            ticker="AAPL",
            model=model,
            last_sequence_scaled=last_sequence_tensor,
            scaler=scaler,
            n_features=n_features,
            close_col_idx=3,
            target_col_idx=13,
            look_back=LOOK_BACK,
            future_steps=FUTURE_PREDICTION_DAYS,
        )

        assert isinstance(returns, np.ndarray)
        assert isinstance(prices, np.ndarray)

    def test_predict_future_wrong_shape(self, sample_model_and_data):
        """Test predict_future with wrong input shape."""
        model, scaler, _, n_features = sample_model_and_data

        # Wrong shape - should be (LOOK_BACK, n_features)
        wrong_shape_sequence = np.random.randn(10, n_features)

        returns, prices = predict_future(
            ticker="AAPL",
            model=model,
            last_sequence_scaled=wrong_shape_sequence,
            scaler=scaler,
            n_features=n_features,
            close_col_idx=3,
            target_col_idx=13,
            look_back=LOOK_BACK,
            future_steps=FUTURE_PREDICTION_DAYS,
        )

        # Should return empty lists for wrong shape
        assert returns == []
        assert prices == []


class TestInverseTransformPredictions:
    """Test suite for inverse_transform_predictions function."""

    @pytest.fixture
    def sample_prediction_data(self):
        """Create sample prediction data for testing."""
        batch_size = 16
        seq_len = 10
        num_quantiles = 3
        n_features = 14

        predictions_scaled = np.random.randn(batch_size, seq_len, num_quantiles)
        actual_scaled = np.random.randn(batch_size, seq_len)
        X_eval = np.random.randn(batch_size, 30, n_features)  # 30 = LOOK_BACK

        # Create scaler
        scaler = StandardScaler()
        sample_data = np.random.randn(100, n_features)
        scaler.fit(sample_data)

        return predictions_scaled, actual_scaled, X_eval, scaler

    def test_inverse_transform_basic(self, sample_prediction_data):
        """Test basic inverse_transform_predictions functionality."""
        predictions_scaled, actual_scaled, X_eval, scaler = sample_prediction_data

        pred_prices, actual_prices = inverse_transform_predictions(
            predictions_scaled=predictions_scaled,
            actual_scaled=actual_scaled,
            X_eval_np=X_eval,
            scaler=scaler,
            close_col_idx=3,
            target_col_idx=13,
        )

        assert isinstance(pred_prices, np.ndarray)
        assert isinstance(actual_prices, np.ndarray)
        assert pred_prices.shape == predictions_scaled.shape
        assert actual_prices.shape == actual_scaled.shape

    def test_inverse_transform_tensor_inputs(self, sample_prediction_data):
        """Test inverse_transform_predictions with tensor inputs."""
        predictions_scaled, actual_scaled, X_eval, scaler = sample_prediction_data

        # Convert to tensors
        predictions_tensor = torch.tensor(predictions_scaled, dtype=torch.float32)
        actual_tensor = torch.tensor(actual_scaled, dtype=torch.float32)
        X_eval_tensor = torch.tensor(X_eval, dtype=torch.float32)

        pred_prices, actual_prices = inverse_transform_predictions(
            predictions_scaled=predictions_tensor,
            actual_scaled=actual_tensor,
            X_eval_np=X_eval_tensor,
            scaler=scaler,
            close_col_idx=3,
            target_col_idx=13,
        )

        assert isinstance(pred_prices, np.ndarray)
        assert isinstance(actual_prices, np.ndarray)


class TestRunTrainingPipeline:
    """Test suite for run_training_pipeline function."""

    @pytest.fixture
    def mock_stock_data(self):
        """Create mock stock data for testing."""
        dates = pd.date_range("2022-01-01", periods=200, freq="D")
        data = {
            "Open": np.random.uniform(100, 200, 200),
            "High": np.random.uniform(150, 250, 200),
            "Low": np.random.uniform(50, 150, 200),
            "Close": np.random.uniform(100, 200, 200),
            "Volume": np.random.randint(1000000, 10000000, 200),
        }
        return pd.DataFrame(data, index=dates)

    @pytest.fixture
    def mock_preprocessing_return(self):
        """Create mock preprocessing return values."""
        scaler = StandardScaler()
        scaler.fit(np.random.randn(100, 14))

        X_train = np.random.randn(50, 30, 14).astype(np.float32)
        y_train = np.random.randn(50, 10).astype(np.float32)
        X_eval = np.random.randn(20, 30, 14).astype(np.float32)
        y_eval = np.random.randn(20, 10).astype(np.float32)

        return (
            scaler,
            X_train,
            y_train,
            X_eval,
            y_eval,
            3,
            13,  # close_column_index, target_column_index
            pd.date_range("2022-01-01", periods=200),  # original_dates_for_plot
            np.random.uniform(100, 200, 200),  # original_prices_for_plot
            pd.date_range("2022-01-01", periods=150),  # processed_dates_for_model
            np.random.randn(30, 14).astype(np.float32),  # last_sequence_scaled
            [f"feature_{i}" for i in range(14)],  # feature_names
            100,  # train_split_idx
            14,  # feature_length
        )

    @patch("src.models.prediction_model.model.StockCache")
    @patch("src.models.prediction_model.model.preprocess_stock_data")
    @patch("src.models.prediction_model.model.optuna.create_study")
    @patch("src.models.prediction_model.model.torch.save")
    @patch("src.models.prediction_model.model.plot_predictions")
    def test_run_training_pipeline_basic_success(
        self,
        mock_plot,
        mock_torch_save,
        mock_optuna,
        mock_preprocess,
        mock_stock_cache,
        mock_stock_data,
        mock_preprocessing_return,
    ):
        """Test successful run_training_pipeline execution."""
        # Setup mocks
        mock_cache_instance = Mock()
        mock_cache_instance.get_historical_data.return_value = mock_stock_data
        mock_stock_cache.return_value = mock_cache_instance

        mock_preprocess.return_value = mock_preprocessing_return

        # Mock Optuna study
        mock_study = Mock()
        mock_study.best_params = {
            "lr": 0.001,
            "d_model": 64,
            "nhead": 4,
            "num_encoder_layers": 2,
            "dim_feedforward": 256,
            "dropout": 0.1,
            "batch_size": 32,
            "epochs": 5,
            "weight_decay": 0.0001,
        }
        mock_study.best_value = 0.5
        mock_optuna.return_value = mock_study

        mock_plot.return_value = "base64_encoded_plot"

        with tempfile.TemporaryDirectory() as temp_dir:
            result = run_training_pipeline(ticker="AAPL", model_storage_root=temp_dir, force_retrain=False)

            assert isinstance(result, dict)
            # Check for expected keys in the result
            expected_keys = ["eval_mae", "eval_mse", "eval_rmse"]
            for key in expected_keys:
                assert key in result
            mock_cache_instance.get_historical_data.assert_called_once_with("AAPL")
            mock_preprocess.assert_called_once()
            mock_optuna.assert_called_once()

    @patch("src.models.prediction_model.model.StockCache")
    def test_run_training_pipeline_no_data(self, mock_stock_cache):
        """Test run_training_pipeline with no stock data."""
        mock_cache_instance = Mock()
        mock_cache_instance.get_historical_data.return_value = None
        mock_stock_cache.return_value = mock_cache_instance

        with tempfile.TemporaryDirectory() as temp_dir:
            result = run_training_pipeline(ticker="INVALID", model_storage_root=temp_dir)

            assert result == {}

    @patch("src.models.prediction_model.model.StockCache")
    def test_run_training_pipeline_empty_data(self, mock_stock_cache):
        """Test run_training_pipeline with empty stock data."""
        mock_cache_instance = Mock()
        mock_cache_instance.get_historical_data.return_value = pd.DataFrame()
        mock_stock_cache.return_value = mock_cache_instance

        with tempfile.TemporaryDirectory() as temp_dir:
            result = run_training_pipeline(ticker="EMPTY", model_storage_root=temp_dir)

            assert result == {}

    @patch("src.models.prediction_model.model.StockCache")
    def test_run_training_pipeline_missing_close_column(self, mock_stock_cache):
        """Test run_training_pipeline with missing Close column."""
        mock_cache_instance = Mock()
        # Create data without Close column
        data = pd.DataFrame(
            {"Open": [100, 101, 102], "High": [105, 106, 107], "Low": [95, 96, 97], "Volume": [1000000, 1100000, 1200000]}
        )
        mock_cache_instance.get_historical_data.return_value = data
        mock_stock_cache.return_value = mock_cache_instance

        with tempfile.TemporaryDirectory() as temp_dir:
            result = run_training_pipeline(ticker="NO_CLOSE", model_storage_root=temp_dir)

            assert result == {}

    @patch("src.models.prediction_model.model.StockCache")
    @patch("src.models.prediction_model.model.preprocess_stock_data")
    def test_run_training_pipeline_preprocessing_failure(self, mock_preprocess, mock_stock_cache, mock_stock_data):
        """Test run_training_pipeline with preprocessing failure."""
        mock_cache_instance = Mock()
        mock_cache_instance.get_historical_data.return_value = mock_stock_data
        mock_stock_cache.return_value = mock_cache_instance

        # Mock preprocessing failure
        mock_preprocess.return_value = (None, None, None, None, None, None, None, None, None, None, None, None, None, None)

        with tempfile.TemporaryDirectory() as temp_dir:
            result = run_training_pipeline(ticker="PREPROCESS_FAIL", model_storage_root=temp_dir)

            assert result == {}

    @patch("src.models.prediction_model.model.plot_predictions")
    @patch("src.models.prediction_model.model.StockCache")
    @patch("src.models.prediction_model.model.preprocess_stock_data")
    @patch("os.path.exists")
    def test_run_training_pipeline_load_checkpoint(
        self, mock_exists, mock_preprocess, mock_stock_cache, mock_plot, mock_stock_data, mock_preprocessing_return
    ):
        """Test run_training_pipeline loading from checkpoint."""
        mock_cache_instance = Mock()
        mock_cache_instance.get_historical_data.return_value = mock_stock_data
        mock_stock_cache.return_value = mock_cache_instance

        # Mock plotting to avoid timezone issues
        mock_plot.return_value = "base64_plot_data"

        mock_preprocess.return_value = mock_preprocessing_return

        # Mock no checkpoint exists to avoid file mocking issues
        mock_exists.return_value = False

        # Mock checkpoint parameters
        checkpoint_params = {
            "lr": 0.001,
            "d_model": 64,
            "nhead": 4,
            "num_encoder_layers": 2,
            "dim_feedforward": 256,
            "dropout": 0.1,
            "batch_size": 32,
            "epochs": 5,
            "weight_decay": 0.0001,
        }

        with tempfile.TemporaryDirectory() as temp_dir:
            with patch("json.load", return_value=checkpoint_params):
                result = run_training_pipeline(ticker="AAPL", model_storage_root=temp_dir, force_retrain=False)

                assert isinstance(result, dict)


class TestPlotPredictions:
    """Test suite for plot_predictions function."""

    @pytest.fixture
    def sample_plot_data(self):
        """Create sample data for plotting tests."""
        # Create timezone-aware dates to match the model's expectation
        dates = pd.date_range("2022-01-01", periods=100, freq="D", tz="America/New_York")
        prices = np.random.uniform(100, 200, 100)

        eval_dates = dates[-30:-10]
        eval_predictions = np.random.uniform(90, 210, (20, 3))  # 20 days, 3 quantiles

        future_dates = pd.date_range(dates[-1] + pd.Timedelta(days=1), periods=10, freq="D", tz="America/New_York")
        future_predictions = np.random.uniform(95, 205, (10, 3))  # 10 days, 3 quantiles

        prediction_input_dates = dates[-40:-30]
        quantiles = [0.1, 0.5, 0.9]

        return (
            dates,
            prices,
            eval_dates,
            eval_predictions,
            future_dates,
            future_predictions,
            prediction_input_dates,
            quantiles,
        )

    @patch("src.models.prediction_model.model.ggplot")
    @patch("src.models.prediction_model.model.base64")
    @patch("src.models.prediction_model.model.io.BytesIO")
    def test_plot_predictions_basic(self, mock_bytesio, mock_base64, mock_ggplot, sample_plot_data):
        """Test basic plot_predictions functionality."""
        (full_dates, full_prices, eval_dates, eval_preds, future_dates, future_preds, input_dates, quantiles) = (
            sample_plot_data
        )

        # Mock the plotting components
        mock_plot = Mock()
        mock_plot.save.return_value = None
        mock_ggplot.return_value = mock_plot

        mock_buffer = Mock()
        mock_bytesio.return_value = mock_buffer
        mock_base64.b64encode.return_value = b"encoded_plot"

        result = plot_predictions(
            ticker="AAPL",
            full_dates=full_dates,
            full_actual_prices=full_prices,
            eval_dates=eval_dates,
            eval_predicted_prices=eval_preds,
            future_dates=future_dates,
            future_predicted_prices=future_preds,
            prediction_input_dates=input_dates,
            quantiles=quantiles,
        )

        assert result == "encoded_plot"
        mock_ggplot.assert_called()
        mock_base64.b64encode.assert_called()

    def test_plot_predictions_empty_data(self):
        """Test plot_predictions with empty data."""
        result = plot_predictions(
            ticker="EMPTY",
            full_dates=pd.Series([], dtype="datetime64[ns]"),
            full_actual_prices=np.array([]),
            eval_dates=pd.Series([], dtype="datetime64[ns]"),
            eval_predicted_prices=np.array([]).reshape(0, 3),
            future_dates=pd.Series([], dtype="datetime64[ns]"),
            future_predicted_prices=np.array([]).reshape(0, 3),
            prediction_input_dates=pd.Series([], dtype="datetime64[ns]"),
            quantiles=[0.1, 0.5, 0.9],
        )

        # Should handle empty data gracefully
        assert result is None or isinstance(result, str)

    @patch("src.models.prediction_model.model.ggplot")
    @patch("src.models.prediction_model.model.base64")
    @patch("src.models.prediction_model.model.io.BytesIO")
    def test_plot_predictions_exception_handling(self, mock_bytesio, mock_base64, mock_ggplot, sample_plot_data):
        """Test plot_predictions exception handling."""
        (full_dates, full_prices, eval_dates, eval_preds, future_dates, future_preds, input_dates, quantiles) = (
            sample_plot_data
        )

        # Mock the plotting components to work until save() fails
        mock_plot = Mock()
        mock_plot.save.side_effect = Exception("Save error")
        mock_ggplot.return_value = mock_plot

        mock_buffer = Mock()
        mock_bytesio.return_value = mock_buffer

        # The function should handle the exception and return None
        with patch("src.models.prediction_model.model.logger") as mock_logger:
            result = plot_predictions(
                ticker="ERROR",
                full_dates=full_dates,
                full_actual_prices=full_prices,
                eval_dates=eval_dates,
                eval_predicted_prices=eval_preds,
                future_dates=future_dates,
                future_predicted_prices=future_preds,
                prediction_input_dates=input_dates,
                quantiles=quantiles,
            )

            # Should return None on exception
            assert result is None
            # Should log the error
            mock_logger.error.assert_called()

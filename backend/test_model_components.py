#!/usr/bin/env python3
"""
Quick test script to verify the enhanced model components work correctly.
Tests the new AttentionPooling and FeatureMaskingLayer without full training.
"""

import os
import sys
import torch
import numpy as np
import pandas as pd

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.models.prediction_model.model import (
    AttentionPooling,
    FeatureMaskingLayer, 
    TimeSeriesTransformer,
    QuantileLoss
)
from src.utils.logger import get_logger

logger = get_logger()

def test_attention_pooling():
    """Test the AttentionPooling component."""
    logger.info("🔍 Testing AttentionPooling...")
    
    d_model = 64
    batch_size = 4
    seq_len = 30
    num_heads = 2
    
    # Create attention pooling layer
    attention_pooling = AttentionPooling(d_model, num_heads)
    
    # Create test input
    x = torch.randn(batch_size, seq_len, d_model)
    
    # Forward pass
    pooled_output, attention_weights = attention_pooling(x)
    
    # Verify shapes
    assert pooled_output.shape == (batch_size, d_model), f"Expected {(batch_size, d_model)}, got {pooled_output.shape}"
    assert attention_weights.shape == (batch_size, 1, seq_len), f"Expected {(batch_size, 1, seq_len)}, got {attention_weights.shape}"
    
    # Verify attention weights sum to 1
    attention_sum = attention_weights.sum(dim=-1)
    assert torch.allclose(attention_sum, torch.ones_like(attention_sum), atol=1e-6), "Attention weights should sum to 1"
    
    logger.info("✅ AttentionPooling test passed")
    return True

def test_feature_masking():
    """Test the FeatureMaskingLayer component."""
    logger.info("🔍 Testing FeatureMaskingLayer...")
    
    input_dim = 20
    batch_size = 4
    seq_len = 30
    
    # Create realistic feature names
    feature_names = [
        'Close', 'High', 'Low', 'Open', 'Volume', 'TargetReturn',  # Price features
        'SMA', 'RSI', 'MACD_Line', 'BB_Upper', 'ATR',  # Technical indicators
        'MarketCap', 'TrailingPE', 'ProfitMargin', 'ROE',  # Fundamentals
        'OptTotalCallVol', 'OptTotalPutVol', 'OptWAvgCallIV',  # Options
        'EMA20', 'STOCH_k', 'CCI'  # More technical indicators
    ]
    
    # Create feature masking layer
    feature_masking = FeatureMaskingLayer(input_dim, feature_names)
    
    # Create test input with some NaN values to simulate missing data
    x = torch.randn(batch_size, seq_len, input_dim)
    
    # Introduce missing data patterns typical for different stock types
    # Missing fundamental data (like for ETFs)
    x[:, :, 11:15] = float('nan')  # Missing fundamentals
    # Missing options data (for some periods)
    x[:, 10:20, 15:18] = float('nan')  # Missing options data
    
    # Forward pass
    masked_output, attention_mask = feature_masking(x)
    
    # Verify shapes
    assert masked_output.shape == x.shape, f"Expected {x.shape}, got {masked_output.shape}"
    assert attention_mask.shape == x.shape, f"Expected {x.shape}, got {attention_mask.shape}"
    
    # Verify no NaN values in output
    assert not torch.isnan(masked_output).any(), "Output should not contain NaN values"
    
    # Verify attention mask reflects missing data
    # Areas with missing data should have lower attention weights
    missing_fundamental_mask = attention_mask[:, :, 11:15].mean()
    available_price_mask = attention_mask[:, :, 0:6].mean()
    assert missing_fundamental_mask < available_price_mask, "Missing data areas should have lower attention weights"
    
    logger.info("✅ FeatureMaskingLayer test passed")
    return True

def test_full_transformer():
    """Test the complete TimeSeriesTransformer with enhanced components."""
    logger.info("🔍 Testing complete TimeSeriesTransformer...")
    
    # Model parameters
    input_dim = 25
    d_model = 64
    nhead = 4
    num_encoder_layers = 2
    dim_feedforward = 256
    dropout = 0.1
    output_dim = 10
    num_quantiles = 3
    
    batch_size = 4
    seq_len = 30
    
    # Create feature names
    feature_names = [
        'Close', 'High', 'Low', 'Open', 'Volume', 'TargetReturn',
        'SMA', 'RSI', 'MACD_Line', 'MACD_Signal', 'BB_Upper', 'BB_Lower', 'ATR', 'OBV',
        'MarketCap', 'TrailingPE', 'ForwardPE', 'ProfitMargin', 'ROE', 'DebtToEquity',
        'OptTotalCallVol', 'OptTotalPutVol', 'OptWAvgCallIV', 'OptWAvgPutIV',
        'EMA20'
    ]
    
    # Create model
    model = TimeSeriesTransformer(
        input_dim=input_dim,
        d_model=d_model,
        nhead=nhead,
        num_encoder_layers=num_encoder_layers,
        dim_feedforward=dim_feedforward,
        dropout=dropout,
        output_dim=output_dim,
        num_quantiles=num_quantiles,
        feature_names=feature_names
    )
    
    # Create test input with missing data patterns
    x = torch.randn(batch_size, seq_len, input_dim)
    
    # Simulate different missing data patterns for different "stock types"
    # Stock 0: Full data (like AAPL)
    # Stock 1: Missing some fundamentals (like growth stock)
    x[1, :, 15:20] = float('nan')  # Missing some fundamentals
    # Stock 2: Missing options data (like small cap)
    x[2, :, 20:24] = float('nan')  # Missing options data
    # Stock 3: Missing fundamentals and options (like ETF)
    x[3, :, 14:24] = float('nan')  # Missing fundamentals and options
    
    # Forward pass
    output = model(x)
    
    # Verify output shape
    expected_shape = (batch_size, output_dim, num_quantiles)
    assert output.shape == expected_shape, f"Expected {expected_shape}, got {output.shape}"
    
    # Verify no NaN values in output
    assert not torch.isnan(output).any(), "Model output should not contain NaN values"
    
    # Verify quantile ordering (lower quantiles should generally be <= higher quantiles)
    # This is a soft constraint as the model is untrained
    q1, q2, q3 = output[:, :, 0], output[:, :, 1], output[:, :, 2]  # 0.1, 0.5, 0.9 quantiles
    
    logger.info(f"   Output shape: {output.shape}")
    logger.info(f"   Output range: [{output.min():.4f}, {output.max():.4f}]")
    logger.info(f"   Quantile means: Q1={q1.mean():.4f}, Q2={q2.mean():.4f}, Q3={q3.mean():.4f}")
    
    logger.info("✅ TimeSeriesTransformer test passed")
    return True

def test_quantile_loss():
    """Test the QuantileLoss function."""
    logger.info("🔍 Testing QuantileLoss...")
    
    quantiles = [0.1, 0.5, 0.9]
    batch_size = 4
    output_dim = 10
    num_quantiles = 3
    
    # Create loss function
    criterion = QuantileLoss(quantiles)
    
    # Create test predictions and targets
    predictions = torch.randn(batch_size, output_dim, num_quantiles, requires_grad=True)
    targets = torch.randn(batch_size, output_dim)
    
    # Compute loss
    loss = criterion(predictions, targets)
    
    # Verify loss properties
    assert loss.item() >= 0, "Loss should be non-negative"
    assert not torch.isnan(loss), "Loss should not be NaN"
    assert loss.requires_grad, "Loss should require gradients"
    
    logger.info(f"   Loss value: {loss.item():.4f}")
    logger.info("✅ QuantileLoss test passed")
    return True

def main():
    """Run all component tests."""
    logger.info("🚀 Starting Enhanced Model Component Tests")
    logger.info("=" * 60)
    
    tests = [
        ("AttentionPooling", test_attention_pooling),
        ("FeatureMaskingLayer", test_feature_masking),
        ("TimeSeriesTransformer", test_full_transformer),
        ("QuantileLoss", test_quantile_loss),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            logger.info(f"\n📋 Running {test_name} test...")
            success = test_func()
            if success:
                passed += 1
                logger.info(f"✅ {test_name} test PASSED")
            else:
                failed += 1
                logger.error(f"❌ {test_name} test FAILED")
        except Exception as e:
            failed += 1
            logger.error(f"❌ {test_name} test FAILED with error: {e}")
            import traceback
            logger.debug(traceback.format_exc())
    
    logger.info("\n" + "=" * 60)
    logger.info("COMPONENT TEST SUMMARY")
    logger.info("=" * 60)
    logger.info(f"Total tests: {len(tests)}")
    logger.info(f"Passed: {passed}")
    logger.info(f"Failed: {failed}")
    logger.info(f"Success rate: {passed/len(tests)*100:.1f}%")
    
    if failed == 0:
        logger.info("🎉 All component tests PASSED! Enhanced model is ready.")
        return True
    else:
        logger.error("💥 Some component tests FAILED. Please check the implementation.")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("\n⏹️  Tests interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"💥 Test script failed: {e}")
        import traceback
        logger.debug(traceback.format_exc())
        sys.exit(1)

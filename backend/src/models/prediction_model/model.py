import base64  # Added for Base64 encoding
import io  # Added for in-memory image saving
import json  # Added for checkpoint parameters
import math
import os

import matplotlib  # Import matplotlib
import numpy as np
import pandas as pd

matplotlib.use("Agg")

import optuna

# ML/DL libraries
import torch
import torch.nn as nn
import torch.optim as optim
from mizani.breaks import date_breaks
from mizani.formatters import date_format
from pandas.tseries.offsets import BDay  # For business day offsets
from plotnine import (
    aes,
    element_text,
    geom_line,
    geom_rect,
    geom_ribbon,
    ggplot,
    labs,
    scale_color_manual,
    scale_fill_manual,
    scale_linetype_manual,
    scale_x_datetime,
    theme,
    theme_bw,
)
from sklearn.metrics import mean_absolute_error, mean_squared_error  # Added for evaluation
from torch.utils.data import DataLoader, TensorDataset

from ...utils.logger import get_logger
from ...utils.stock_cache import StockCache
from .feature_engineering import preprocess_stock_data

logger = get_logger()

# Configuration
# MODEL_DIR is now passed as an argument (model_storage_root)
if torch.mps.is_available():
    DEVICE = torch.device("mps")
elif torch.cuda.is_available():
    DEVICE = torch.device("cuda")
else:
    DEVICE = torch.device("cpu")
logger.info(f"Using device: {DEVICE}")

# Model Configuration
LOOK_BACK = 30  # Number of previous time steps to use as input variables
# Optuna will search for EPOCHS, BATCH_SIZE, LR etc.
N_OPTUNA_TRIALS = 30
FUTURE_PREDICTION_DAYS = 10
QUANTILES = [0.1, 0.5, 0.9]  # For uncertainty intervals
ADAPTIVE_BIAS_WINDOW = 30  # Number of recent evaluation points for adaptive bias




# --- PyTorch Transformer Model Definition ---
class PositionalEncoding(nn.Module):
    def __init__(self, d_model, dropout=0.1, max_len=5000):
        super(PositionalEncoding, self).__init__()
        self.dropout = nn.Dropout(p=dropout)

        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        self.register_buffer("pe", pe)

    def forward(self, x):
        # x shape: (seq_len, batch_size, d_model)
        x = x + self.pe[: x.size(0), :]
        return self.dropout(x)


class AttentionPooling(nn.Module):
    """
    Attention pooling mechanism to aggregate sequence information.
    Uses multi-head attention to compute weighted average of sequence elements.
    """
    def __init__(self, d_model, num_heads=1):
        super(AttentionPooling, self).__init__()
        self.attention = nn.MultiheadAttention(d_model, num_heads=num_heads, batch_first=True)
        self.query = nn.Parameter(torch.randn(1, 1, d_model))

    def forward(self, x):
        """
        Args:
            x: (batch_size, seq_len, d_model)
        Returns:
            pooled_output: (batch_size, d_model)
            attention_weights: (batch_size, 1, seq_len)
        """
        batch_size = x.size(0)
        # Expand query to match batch size
        query = self.query.expand(batch_size, -1, -1)

        # Apply attention pooling
        attn_output, attn_weights = self.attention(query, x, x)

        # Return squeezed output and attention weights
        return attn_output.squeeze(1), attn_weights


class FeatureMaskingLayer(nn.Module):
    """
    Feature masking layer with learnable feature gates and dynamic importance weighting.
    Applies both static feature gates and dynamic importance based on input statistics.
    """
    def __init__(self, input_dim):
        super(FeatureMaskingLayer, self).__init__()
        # Learnable feature gates (static importance)
        self.feature_gates = nn.Parameter(torch.ones(input_dim))
        # Dynamic importance network
        self.feature_importance = nn.Linear(input_dim, input_dim)

    def forward(self, x):
        """
        Args:
            x: (batch_size, seq_len, input_dim)
        Returns:
            masked_features: (batch_size, seq_len, input_dim)
        """
        # Compute dynamic feature importance based on sequence statistics
        # Use mean across sequence dimension to get feature statistics
        sequence_stats = x.mean(dim=1)  # (batch_size, input_dim)
        importance_weights = torch.sigmoid(self.feature_importance(sequence_stats))

        # Apply static feature gates
        gated_features = x * self.feature_gates.unsqueeze(0).unsqueeze(0)

        # Apply dynamic importance weighting
        weighted_features = gated_features * importance_weights.unsqueeze(1)

        return weighted_features


class TimeSeriesTransformer(nn.Module):
    def __init__(self, input_dim, d_model, nhead, num_encoder_layers, dim_feedforward, dropout, output_dim=1, num_quantiles=1):
        super(TimeSeriesTransformer, self).__init__()
        self.d_model = d_model
        self.feature_masking = FeatureMaskingLayer(input_dim)
        self.embedding = nn.Linear(input_dim, d_model)
        self.pos_encoder = PositionalEncoding(d_model, dropout)
        encoder_layers = nn.TransformerEncoderLayer(d_model, nhead, dim_feedforward, dropout, batch_first=True)
        self.transformer_encoder = nn.TransformerEncoder(encoder_layers, num_encoder_layers)
        self.attention_pooling = AttentionPooling(d_model, num_heads=max(1, nhead // 2))
        self.decoder = nn.Linear(d_model, output_dim * num_quantiles)
        self.output_dim = output_dim
        self.num_quantiles = num_quantiles

        self.init_weights()

    def init_weights(self):
        initrange = 0.1
        self.embedding.weight.data.uniform_(-initrange, initrange)
        self.decoder.bias.data.zero_()
        self.decoder.weight.data.uniform_(-initrange, initrange)

    def forward(self, src):
        # src shape: (batch_size, seq_len, input_dim)

        # Apply feature masking
        src = self.feature_masking(src)

        # Embedding and positional encoding
        src = self.embedding(src) * math.sqrt(self.d_model)
        src = self.pos_encoder(src.permute(1, 0, 2))  # permute to (seq_len, batch_size, d_model)

        # Transformer encoder
        output = self.transformer_encoder(src.permute(1, 0, 2))  # permute back to (batch_size, seq_len, d_model)

        # Apply attention pooling instead of last timestep extraction
        pooled_output, attention_weights = self.attention_pooling(output)

        # Decode the pooled representation
        output = self.decoder(pooled_output)

        # Reshape to (batch_size, output_dim, num_quantiles) for quantile regression
        output = output.view(-1, self.output_dim, self.num_quantiles)
        return output


class QuantileLoss(nn.Module):
    """Pinball Loss Function."""

    def __init__(self, quantiles):
        super().__init__()
        self.quantiles = torch.tensor(quantiles, dtype=torch.float32)

    def forward(self, preds, target):
        # preds: (batch_size, seq_len, num_quantiles)
        # target: (batch_size, seq_len)
        if self.quantiles.device != preds.device:
            self.quantiles = self.quantiles.to(preds.device)

        target_unsqueezed = target.unsqueeze(-1)  # Shape: (batch_size, seq_len, 1)
        errors = target_unsqueezed - preds  # Broadcasts to (batch_size, seq_len, num_quantiles)

        q_tensor = self.quantiles.view(1, 1, -1)  # Shape: (1, 1, num_quantiles)
        loss = torch.max((q_tensor - 1) * errors, q_tensor * errors)

        return loss.mean()


def objective(trial, X_train_np, y_train_np, X_eval_np, y_eval_np, input_dim):
    """Optuna objective function. Trains model and returns eval loss (Quantile Loss on scaled returns)."""
    # Hyperparameters to tune
    lr = trial.suggest_float("lr", 1e-6, 1e-2, log=True)
    d_model = trial.suggest_categorical("d_model", [32, 64, 128, 256])
    nhead = trial.suggest_categorical("nhead", [2, 4, 8])
    num_encoder_layers = trial.suggest_int("num_encoder_layers", 1, 6)
    dim_feedforward = trial.suggest_categorical("dim_feedforward", [128, 256, 512, 1024])
    dropout = trial.suggest_float("dropout", 0.1, 0.5)
    batch_size = trial.suggest_categorical("batch_size", [16, 32, 64, 128])
    epochs = trial.suggest_int("epochs", 20, 100)  # Reduced max epochs for Optuna trials for speed
    weight_decay = trial.suggest_float("weight_decay", 1e-6, 1e-2, log=True)

    if d_model % nhead != 0:
        raise optuna.exceptions.TrialPruned("d_model must be divisible by nhead")

    # input_dim is now the number of features including the target return
    model = TimeSeriesTransformer(
        input_dim,
        d_model,
        nhead,
        num_encoder_layers,
        dim_feedforward,
        dropout,
        output_dim=FUTURE_PREDICTION_DAYS,  # Set output dimension to predict a sequence
        num_quantiles=len(QUANTILES),
    ).to(DEVICE)
    optimizer = optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)
    # Criterion is now QuantileLoss
    criterion = QuantileLoss(QUANTILES)

    X_train_tensor = torch.tensor(X_train_np, dtype=torch.float32).to(DEVICE)
    y_train_tensor = torch.tensor(y_train_np, dtype=torch.float32).to(DEVICE)
    X_eval_tensor = torch.tensor(X_eval_np, dtype=torch.float32).to(DEVICE)
    y_eval_tensor = torch.tensor(y_eval_np, dtype=torch.float32).to(DEVICE)

    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    eval_dataset = TensorDataset(X_eval_tensor, y_eval_tensor)
    eval_loader = DataLoader(eval_dataset, batch_size=batch_size, shuffle=False)

    min_eval_loss = float("inf")

    for epoch in range(epochs):
        model.train()
        epoch_train_loss = 0
        for X_batch, y_batch in train_loader:
            optimizer.zero_grad()
            output = model(X_batch)
            loss = criterion(output, y_batch)
            loss.backward()
            optimizer.step()
            epoch_train_loss += loss.item()

        model.eval()
        epoch_eval_loss = 0
        with torch.no_grad():
            for X_batch, y_batch in eval_loader:
                output = model(X_batch)
                loss = criterion(output, y_batch)
                epoch_eval_loss += loss.item()

        avg_eval_loss = epoch_eval_loss / len(eval_loader)
        if avg_eval_loss < min_eval_loss:
            min_eval_loss = avg_eval_loss

        trial.report(avg_eval_loss, epoch)
        if trial.should_prune():
            raise optuna.exceptions.TrialPruned()

    # Optuna objective returns the minimum average evaluation loss (Quantile Loss on scaled returns)
    return min_eval_loss


def inverse_transform_predictions(predictions_scaled, actual_scaled, X_eval_np, scaler, close_col_idx, target_col_idx):
    """
    Converts scaled return predictions back to price predictions for multiple quantiles.
    """
    if isinstance(predictions_scaled, torch.Tensor):
        predictions_scaled = predictions_scaled.detach().cpu().numpy()
    if isinstance(actual_scaled, torch.Tensor):
        actual_scaled = actual_scaled.detach().cpu().numpy()
    if isinstance(X_eval_np, torch.Tensor):
        X_eval_np = X_eval_np.detach().cpu().numpy()

    # predictions_scaled shape: (num_samples, FUTURE_PREDICTION_DAYS, num_quantiles)
    # actual_scaled shape: (num_samples, FUTURE_PREDICTION_DAYS)
    return_mean = scaler.mean_[target_col_idx]
    return_scale = scaler.scale_[target_col_idx]
    close_mean = scaler.mean_[close_col_idx]
    close_scale = scaler.scale_[close_col_idx]

    pred_returns = predictions_scaled * return_scale + return_mean  # Broadcasts correctly
    actual_returns = actual_scaled * return_scale + return_mean

    last_actual_close_scaled = X_eval_np[:, -1, close_col_idx]
    last_actual_close = last_actual_close_scaled * close_scale + close_mean

    # Expand last_actual_close to be broadcastable with the returns arrays
    last_actual_close_expanded = last_actual_close[:, np.newaxis]  # (num_samples, 1)

    # Calculate predicted and actual prices iteratively
    pred_prices = np.zeros_like(pred_returns)  # (num_samples, FUTURE_PREDICTION_DAYS, num_quantiles)
    actual_prices = np.zeros_like(actual_returns)  # (num_samples, FUTURE_PREDICTION_DAYS)

    # Initialize the first predicted price for each quantile
    pred_prices[:, 0, :] = last_actual_close_expanded * (1 + pred_returns[:, 0, :])
    actual_prices[:, 0] = last_actual_close_expanded[:, 0] * (1 + actual_returns[:, 0])

    for i in range(1, predictions_scaled.shape[1]):
        pred_prices[:, i, :] = pred_prices[:, i - 1, :] * (1 + pred_returns[:, i, :])
        actual_prices[:, i] = actual_prices[:, i - 1] * (1 + actual_returns[:, i])

    return pred_prices, actual_prices


def predict_future(
    ticker: str,
    model,
    last_sequence_scaled,
    scaler,
    n_features,
    close_col_idx,
    target_col_idx,
    look_back,
    future_steps,
    bias_correction: float = 0.0,
):
    """
    Predicts future price returns and prices using a direct multi-step forecast for multiple quantiles.
    """
    model.eval()

    if isinstance(last_sequence_scaled, torch.Tensor):
        current_sequence = last_sequence_scaled.detach().cpu().numpy()
    else:
        current_sequence = last_sequence_scaled.copy()

    if current_sequence.ndim == 2:
        current_sequence = np.expand_dims(current_sequence, axis=0)

    if current_sequence.shape != (1, look_back, n_features):
        logger.error(
            f"predict_future: last_sequence_scaled has unexpected shape {current_sequence.shape}. Expected (1, {look_back}, {n_features})"
        )
        return [], []

    with torch.no_grad():
        input_tensor = torch.tensor(current_sequence, dtype=torch.float32).to(DEVICE)
        predicted_returns_scaled = model(input_tensor)  # Shape: (1, future_steps, num_quantiles)
        predicted_returns_scaled = predicted_returns_scaled.squeeze(0).cpu().numpy()  # Shape: (future_steps, num_quantiles)

    last_actual_close_scaled = last_sequence_scaled[-1, close_col_idx]
    last_actual_close_unscaled = last_actual_close_scaled * scaler.scale_[close_col_idx] + scaler.mean_[close_col_idx]

    future_pred_returns = predicted_returns_scaled * scaler.scale_[target_col_idx] + scaler.mean_[target_col_idx]  # Broadcasts

    future_prices = np.zeros_like(future_pred_returns)  # Shape: (future_steps, num_quantiles)

    # Iteratively calculate future prices for each quantile
    # Initialize first step
    future_prices[0, :] = last_actual_close_unscaled * (1 + future_pred_returns[0, :])
    # Iterate for subsequent steps
    for i in range(1, future_pred_returns.shape[0]):
        future_prices[i, :] = future_prices[i - 1, :] * (1 + future_pred_returns[i, :])

    logger.info(f"[{ticker}] Future predicted prices (pre-correction, quantiles): {np.round(future_prices, 2)}")

    # Apply the bias correction to the final price predictions (all quantiles)
    future_prices_corrected = future_prices + bias_correction
    logger.info(f"[{ticker}] Applied bias correction of: {bias_correction:.4f}")

    logger.info(
        f"[{ticker}] Future predicted prices (next {future_steps} days, corrected, quantiles): {np.round(future_prices_corrected, 2)}"
    )

    return future_pred_returns, future_prices_corrected


# --- Main Processing Function for a Single Ticker ---
def run_training_pipeline(ticker: str, model_storage_root: str, force_retrain: bool = False) -> dict:
    logger.info(f"--- Main: Starting Transformer stock prediction for {ticker} (Force Retrain: {force_retrain}) ---")

    # model_storage_root is the base directory like 'backend/prediction_model_data'
    # ticker_model_dir is specific to the ticker, e.g., 'backend/prediction_model_data/AAPL'
    ticker_model_dir = os.path.join(model_storage_root, ticker)
    os.makedirs(ticker_model_dir, exist_ok=True)  # Ensure ticker-specific directory exists

    logger.info(f"[{ticker}] Fetching data from StockCache...")
    stock_cache_instance = StockCache()
    stock_data = stock_cache_instance.get_historical_data(ticker)

    if stock_data is None or stock_data.empty:
        logger.error(f"[{ticker}] No data from StockCache. Skipping.")
        return {}

    if "Volume" in stock_data.columns:
        stock_data["Volume"] = stock_data["Volume"].astype(np.float64)
    else:
        logger.warning(f"[{ticker}] 'Volume' column not found in cached data.")

    if "Close" not in stock_data.columns:
        logger.error(f"[{ticker}] 'Close' column missing. Skipping.")
        return {}

    stock_data = stock_data.sort_index()
    eval_period_days = 63
    # future_prediction_days is now a global FUTURE_PREDICTION_DAYS

    (
        scaler,
        X_train_np,
        y_train_np,
        X_eval_np,
        y_eval_actual_scaled_np,
        close_column_index,
        target_column_index,
        original_dates_for_plot,
        original_prices_for_plot,
        processed_dates_for_model,
        last_sequence_scaled,
        feature_names,
        train_split_idx,
        feature_length,
    ) = preprocess_stock_data(
        stock_data_full=stock_data,
        look_back=LOOK_BACK,
        eval_period_days=eval_period_days,
        ticker=ticker,
        future_prediction_days=FUTURE_PREDICTION_DAYS,
        ticker_for_log=ticker,
    )

    if scaler is None:
        logger.error(f"[{ticker}] Preprocessing failed. Skipping.")
        return {}

    if X_train_np.ndim == 3:
        n_features = X_train_np.shape[2]
    elif last_sequence_scaled is not None and last_sequence_scaled.ndim == 2:
        n_features = last_sequence_scaled.shape[1]
        logger.warning(f"[{ticker}] Training data possibly empty, inferring n_features from last_sequence_scaled.")
    else:
        logger.error(f"[{ticker}] Cannot determine n_features. Skipping.")
        return {}
    logger.info(f"[{ticker}] Number of features for model input: {n_features}")

    if X_train_np.size == 0 and (last_sequence_scaled is None or last_sequence_scaled.size == 0):
        logger.error(f"[{ticker}] Training data and last sequence are empty. Skipping.")
        return {}

    best_params = None
    loaded_from_checkpoint = False
    checkpoint_model_path = os.path.join(ticker_model_dir, f"{ticker}_transformer_checkpoint.pth")
    checkpoint_params_path = os.path.join(ticker_model_dir, f"{ticker}_transformer_checkpoint_params.json")

    if not force_retrain and os.path.exists(checkpoint_model_path) and os.path.exists(checkpoint_params_path):
        logger.info(f"[{ticker}] Found existing checkpoint and force_retrain is False.")
        try:
            with open(checkpoint_params_path, "r") as f:
                best_params = json.load(f)
            required_params = [
                "lr",
                "d_model",
                "nhead",
                "num_encoder_layers",
                "dim_feedforward",
                "dropout",
                "batch_size",
                "epochs",
                "weight_decay",
            ]
            if all(key in best_params for key in required_params):
                logger.info(f"[{ticker}] Loaded hyperparameters from checkpoint: {best_params}")
                loaded_from_checkpoint = True  # Mark true only if params are successfully loaded
            else:
                logger.warning(f"[{ticker}] Checkpoint params missing keys. Will re-run Optuna.")
                best_params = None  # Ensure Optuna runs if params are incomplete
        except Exception as e:
            logger.error(f"[{ticker}] Error loading checkpoint params: {e}. Will re-run Optuna.")
            best_params = None  # Ensure Optuna runs on error
    elif force_retrain:
        logger.info(f"[{ticker}] force_retrain is True. Optuna will be run.")
    else:  # No checkpoint found and not forcing retrain
        logger.info(f"[{ticker}] No checkpoint found (or force_retrain=False and issue with checkpoint). Optuna will be run.")

    # Run Optuna if best_params are not loaded from a valid checkpoint
    if not loaded_from_checkpoint:  # This condition means Optuna should run
        if X_train_np.size == 0 or y_train_np.size == 0:
            logger.error(
                f"[{ticker}] Not enough training data for Optuna study (X_train_np size: {X_train_np.size}, y_train_np size: {y_train_np.size}). Skipping Optuna and training."
            )
            return {}

        logger.info(f"[{ticker}] Starting Optuna study...")
        study = optuna.create_study(direction="minimize", pruner=optuna.pruners.MedianPruner())
        study.optimize(
            lambda trial: objective(trial, X_train_np, y_train_np, X_eval_np, y_eval_actual_scaled_np, n_features),
            n_trials=N_OPTUNA_TRIALS,
        )
        best_params = study.best_params
        logger.info(f"[{ticker}] Optuna finished. Best params: {best_params}, Best MSE: {study.best_value}")
        try:
            with open(checkpoint_params_path, "w") as f:
                json.dump(best_params, f)
            logger.info(f"[{ticker}] Best hyperparameters saved to {checkpoint_params_path}")
        except Exception as e:
            logger.error(f"[{ticker}] Error saving best hyperparameters: {e}")

    if not best_params:  # If Optuna failed or was skipped and no params loaded
        logger.error(f"[{ticker}] No best parameters available. Cannot train model.")
        return {}

    if best_params["d_model"] % best_params["nhead"] != 0:
        logger.error(
            f"[{ticker}] FATAL: d_model ({best_params['d_model']}) not divisible by nhead ({best_params['nhead']}). Cannot initialize model."
        )
        return {}

    final_model = TimeSeriesTransformer(
        input_dim=n_features,
        d_model=best_params["d_model"],
        nhead=best_params["nhead"],
        num_encoder_layers=best_params["num_encoder_layers"],
        dim_feedforward=best_params["dim_feedforward"],
        dropout=best_params["dropout"],
        output_dim=FUTURE_PREDICTION_DAYS,  # Ensure final model also predicts a sequence
        num_quantiles=len(QUANTILES),
    ).to(DEVICE)

    # Attempt to load model weights if params were loaded from checkpoint AND force_retrain is False
    # If force_retrain is True, we skip loading weights and train fresh.
    should_load_weights = loaded_from_checkpoint and not force_retrain
    final_criterion = None  # Initialize final_criterion

    if should_load_weights:
        try:
            final_model.load_state_dict(torch.load(checkpoint_model_path, map_location=DEVICE))
            logger.info(f"[{ticker}] Loaded final model weights from checkpoint (force_retrain=False).")
        except Exception as e:
            logger.error(f"[{ticker}] Error loading model weights from checkpoint: {e}. Training from scratch instead.")
            should_load_weights = False  # Force training if weight loading fails

    # Train the model if:
    # 1. We are not loading weights (either because no valid checkpoint params, or weight loading failed)
    # 2. Or if force_retrain is True (regardless of checkpoint status)
    if not should_load_weights or force_retrain:
        # Combine train and eval sets to train the final model on all available data before prediction
        logger.info(f"[{ticker}] Preparing final training data by combining training and evaluation sets.")
        if X_train_np.size > 0 and X_eval_np.size > 0 and y_eval_actual_scaled_np.size > 0:
            X_train_final = np.concatenate((X_train_np, X_eval_np), axis=0)
            y_train_final = np.concatenate((y_train_np, y_eval_actual_scaled_np), axis=0)
            logger.info(
                f"[{ticker}] Combined training + evaluation data for final model training. Final X shape: {X_train_final.shape}"
            )
        else:
            logger.warning(
                f"[{ticker}] Evaluation set is empty or initial training set is empty, using original training set for final training."
            )
            X_train_final = X_train_np
            y_train_final = y_train_np

        if X_train_final.size == 0 or y_train_final.size == 0:
            logger.error(f"[{ticker}] Not enough data for final training after combining. Skipping training.")
            return {}

        log_prefix = f"[{ticker}] (Forced Retrain) " if force_retrain and should_load_weights else f"[{ticker}] "
        logger.info(f"{log_prefix}Training final model on combined dataset...")

        final_optimizer = optim.AdamW(
            final_model.parameters(), lr=best_params["lr"], weight_decay=best_params.get("weight_decay", 1e-4)
        )
        final_criterion = QuantileLoss(QUANTILES)
        final_batch_size = best_params["batch_size"]
        final_epochs = best_params["epochs"]

        X_train_tensor = torch.tensor(X_train_final, dtype=torch.float32).to(DEVICE)
        y_train_tensor = torch.tensor(y_train_final, dtype=torch.float32).to(DEVICE)
        train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
        train_loader = DataLoader(train_dataset, batch_size=final_batch_size, shuffle=True)

        for epoch in range(final_epochs):
            final_model.train()
            epoch_loss = 0
            for X_batch, y_batch in train_loader:
                final_optimizer.zero_grad()
                output = final_model(X_batch)
                loss = final_criterion(output, y_batch)
                loss.backward()
                final_optimizer.step()
                epoch_loss += loss.item()
            avg_epoch_loss = epoch_loss / len(train_loader)
            if (epoch + 1) % 10 == 0 or epoch == 0 or epoch == final_epochs - 1:
                logger.info(f"{log_prefix}Epoch [{epoch + 1}/{final_epochs}], Loss: {avg_epoch_loss:.6f}")
        try:
            torch.save(final_model.state_dict(), checkpoint_model_path)
            logger.info(f"{log_prefix}Final model state saved to {checkpoint_model_path}")
            # Always save params after successful training or optuna
            with open(checkpoint_params_path, "w") as f:
                json.dump(best_params, f)
            logger.info(f"{log_prefix}Hyperparameters (re)saved to {checkpoint_params_path}")
        except Exception as e:
            logger.error(f"{log_prefix}Error saving final model state or parameters: {e}")

    eval_predicted_prices, eval_actual_prices = np.array([]), np.array([])
    eval_mse_price, eval_mae_price, eval_rmse_price = np.nan, np.nan, np.nan  # Initialize as NaN
    eval_bias = 0.0  # Initialize bias correction
    feature_importance_plot_base64 = None  # Initialize

    if X_eval_np.size > 0 and y_eval_actual_scaled_np.size > 0:
        logger.info(f"[{ticker}] Evaluating final model...")
        final_model.eval()  # Ensure model is in eval mode for permutation importance too
        with torch.no_grad():
            X_eval_tensor = torch.tensor(X_eval_np, dtype=torch.float32).to(DEVICE)
            y_eval_tensor_for_loss = torch.tensor(y_eval_actual_scaled_np, dtype=torch.float32).to(
                DEVICE
            )  # For loss calculation
            predictions_scaled = final_model(X_eval_tensor)
            eval_predicted_prices, eval_actual_prices = inverse_transform_predictions(
                predictions_scaled, y_eval_actual_scaled_np, X_eval_np, scaler, close_column_index, target_column_index
            )

            if eval_predicted_prices.size > 0 and eval_actual_prices.size > 0:
                # Calculate bias as the mean difference between actual and predicted prices on the evaluation set.
                # Bias is calculated based on the median prediction (the middle quantile).
                median_quantile_idx = len(QUANTILES) // 2
                median_predicted_prices = eval_predicted_prices[:, :, median_quantile_idx]
                price_errors = eval_actual_prices - median_predicted_prices

                # Adaptive Bias Correction (Rolling Bias)
                rolling_bias_window = min(ADAPTIVE_BIAS_WINDOW, len(price_errors))
                if rolling_bias_window > 0:
                    # Use the last N points for bias calculation
                    recent_price_errors = price_errors[-rolling_bias_window:]
                    eval_bias = np.mean(recent_price_errors)
                    logger.info(
                        f"[{ticker}] Calculated adaptive evaluation price bias (last {rolling_bias_window} points): {eval_bias:.4f}"
                    )
                else:
                    # Fallback for safety, though this path is unlikely
                    eval_bias = np.mean(price_errors)
                    logger.info(f"[{ticker}] Calculated evaluation price bias (full set): {eval_bias:.4f}")

                eval_mae_price = mean_absolute_error(eval_actual_prices, median_predicted_prices)
                eval_mse_price = mean_squared_error(eval_actual_prices, median_predicted_prices)
                eval_rmse_price = np.sqrt(eval_mse_price)
                logger.info(
                    f"[{ticker}] Evaluation Metrics (on Median Predicted Prices): MAE=${eval_mae_price:.4f}, MSE=${eval_mse_price:.4f}, RMSE=${eval_rmse_price:.4f}"
                )
            else:
                logger.warning(f"[{ticker}] Evaluation price arrays are empty. Cannot calculate metrics or bias.")

    else:
        logger.warning(f"[{ticker}] Evaluation set empty. Skipping eval metrics.")

    eval_dates_for_plot = pd.Index([])  # Initialize
    if eval_actual_prices.size > 0:
        eval_actual_prices_for_plot = eval_actual_prices[:, 0]
        # For plotting, we only need the median prediction from the evaluation phase
        median_quantile_idx = len(QUANTILES) // 2
        eval_predicted_prices_for_plot = eval_predicted_prices[:, 0, median_quantile_idx]

        num_eval_points = len(eval_actual_prices_for_plot)

        if processed_dates_for_model is not None and not processed_dates_for_model.empty:
            # The evaluation dates start at the train_split_idx and we take num_eval_points from there
            if len(processed_dates_for_model) >= train_split_idx + num_eval_points:
                eval_dates_for_plot = processed_dates_for_model[train_split_idx : train_split_idx + num_eval_points]
            else:
                logger.warning(
                    f"[{ticker}] Not enough processed_dates_for_model to align with eval_actual_prices. "
                    f"Length mismatch or index out of bounds. Dates len: {len(processed_dates_for_model)}, Required: {train_split_idx + num_eval_points}. Eval dates will be empty."
                )
        else:
            logger.warning(f"[{ticker}] processed_dates_for_model is empty. Cannot determine eval_dates_for_plot.")

    future_pred_returns, future_prices = predict_future(
        ticker=ticker,
        model=final_model,
        last_sequence_scaled=last_sequence_scaled,
        scaler=scaler,
        n_features=n_features,
        close_col_idx=close_column_index,
        target_col_idx=target_column_index,
        look_back=LOOK_BACK,
        future_steps=FUTURE_PREDICTION_DAYS,
        bias_correction=eval_bias,
    )

    # Determine timezone from historical data to apply to future dates
    future_dates_tz = None
    if processed_dates_for_model is not None and not processed_dates_for_model.empty:
        future_dates_tz = processed_dates_for_model.tz

    last_historical_date_for_future = (
        processed_dates_for_model[-1]
        if processed_dates_for_model is not None and not processed_dates_for_model.empty
        else pd.Timestamp.now(tz=future_dates_tz or "UTC") - BDay(FUTURE_PREDICTION_DAYS + 1)
    )

    future_dates = pd.date_range(
        start=last_historical_date_for_future + BDay(1), periods=FUTURE_PREDICTION_DAYS, freq="B", tz=future_dates_tz
    )

    def format_dates(dates_series_or_index, ticker_log_prefix=""):
        if dates_series_or_index is None:
            return []
        if not hasattr(dates_series_or_index, "__iter__"):
            return [str(dates_series_or_index)]
        if len(dates_series_or_index) == 0:
            return []

        try:
            if all(hasattr(d, "strftime") for d in dates_series_or_index):
                return [d.strftime("%Y-%m-%dT%H:%M:%S") for d in dates_series_or_index]
            else:
                pd_dates = pd.to_datetime(dates_series_or_index, errors="coerce")
                valid_pd_dates = pd_dates[~pd_dates.isna()]
                if len(valid_pd_dates) < len(pd_dates):
                    logger.warning(f"{ticker_log_prefix}format_dates: Some dates could not be converted to datetime objects.")
                return valid_pd_dates.strftime("%Y-%m-%dT%H:%M:%S").tolist()
        except Exception as e:
            logger.warning(
                f"{ticker_log_prefix}format_dates: Could not convert dates to datetime objects for formatting: {e}. Fallback to simple string conversion."
            )
            return [str(d) for d in dates_series_or_index]

    # --- Define date ranges for plotting ---
    # The 'Prediction Input' is the look-back period right before the evaluation starts.
    prediction_input_start_idx = max(0, train_split_idx - LOOK_BACK)
    prediction_input_dates_for_plot = processed_dates_for_model[prediction_input_start_idx:train_split_idx]

    # --- Plotting --- (Using the modified plot_predictions)
    plot_image_base64 = plot_predictions(
        ticker=ticker,
        full_dates=original_dates_for_plot,
        full_actual_prices=original_prices_for_plot,
        eval_dates=eval_dates_for_plot,
        eval_predicted_prices=eval_predicted_prices_for_plot if "eval_predicted_prices_for_plot" in locals() else np.array([]),
        future_dates=future_dates,
        future_predicted_prices=future_prices,  # Now has quantile dimension
        prediction_input_dates=prediction_input_dates_for_plot,
        quantiles=QUANTILES,
    )

    # Prepare output for frontend
    predictions_output = {}
    if plot_image_base64:
        predictions_output["plot_image_base64"] = plot_image_base64  # Store Base64 string
    else:
        predictions_output["plot_image_base64"] = None
        logger.error(f"[{ticker}] Plot Base64 string was not generated, plot_image_base64 will be None.")

    # Include future prediction data for numerical display on frontend
    formatted_future_dates = format_dates(future_dates, f"[{ticker}] FutureDatesForOutput ")
    # future_prices is now (n_days, n_quantiles)
    future_price_list_quantiles = future_prices.tolist() if isinstance(future_prices, np.ndarray) else list(future_prices)
    median_quantile_idx = len(QUANTILES) // 2

    predictions_output["future_predictions_data"] = []
    for i in range(len(formatted_future_dates)):
        if i < len(future_price_list_quantiles):
            price_quantiles = future_price_list_quantiles[i]
            predictions_output["future_predictions_data"].append(
                {
                    "date": formatted_future_dates[i],
                    # Median prediction for the main 'price'
                    "price": price_quantiles[median_quantile_idx],
                    # Adding the lower and upper bounds
                    "lower_bound": price_quantiles[0],
                    "upper_bound": price_quantiles[-1],
                }
            )
        else:
            # This case should ideally not happen if lengths are consistent
            logger.warning(f"[{ticker}] Mismatch between future dates and prices length at index {i}.")

    predictions_output["eval_mse"] = eval_mse_price
    predictions_output["eval_mae"] = eval_mae_price
    predictions_output["eval_rmse"] = eval_rmse_price
    predictions_output["feature_importance_plot_base64"] = feature_importance_plot_base64  # Add new plot

    logger.info(f"--- Completed process for {ticker}, returning image path and future data. ---")
    return predictions_output



def plot_predictions(
    ticker,
    full_dates,
    full_actual_prices,
    eval_dates,
    eval_predicted_prices,
    future_dates,
    future_predicted_prices,
    prediction_input_dates,
    quantiles,
):
    """
    Generates a clean plot of stock predictions with shaded context regions and uncertainty intervals.
    Returns a Base64 encoded string of the plot image if successful, otherwise None.
    """

    # --- Aesthetics ---
    color_map = {
        "Historical Price": "#4A4A4A",
        "Median Predicted": "#007BFF",  # Brighter Blue
    }
    linetype_map = {"Historical Price": "solid", "Median Predicted": "solid"}
    region_color_map = {"Evaluation Period": "#E53935", "Prediction Input": "#F5A623"}  # Red  # Orange
    uncertainty_fill_color = "#007BFF"

    # --- Prepare Line Data ---
    hist_df = pd.DataFrame(data={"Date": full_dates, "Price": full_actual_prices, "Type": "Historical Price"})

    # --- Prepare Future Prediction Data (with Quantiles) ---
    median_quantile_idx = len(quantiles) // 2
    future_pred_median = future_predicted_prices[:, median_quantile_idx]
    future_pred_lower = future_predicted_prices[:, 0]
    future_pred_upper = future_predicted_prices[:, -1]

    future_pred_df = pd.DataFrame(
        data={
            "Date": future_dates,
            "Price": future_pred_median,
            "lower": future_pred_lower,
            "upper": future_pred_upper,
            "Type": "Median Predicted",
        }
    )

    # --- Connect Future Predictions to the Last Actual Price ---
    if not future_pred_df.empty and not hist_df.empty:
        last_actual_date = hist_df["Date"].max()
        last_actual_price = hist_df.loc[hist_df["Date"] == last_actual_date, "Price"].iloc[0]

        # Connection point for the median line
        connection_point_median_df = pd.DataFrame(
            [{"Date": last_actual_date, "Price": last_actual_price, "Type": "Median Predicted"}]
        )

        # For the uncertainty band, it should also start from the last actual price
        connection_point_uncertainty_df = pd.DataFrame(
            [{"Date": last_actual_date, "lower": last_actual_price, "upper": last_actual_price}]
        )

        # Update the main future_pred_df with connection points
        future_pred_df_for_concat = future_pred_df.copy()
        future_pred_df_for_concat = pd.concat(
            [connection_point_median_df[["Date", "Price", "Type"]], future_pred_df_for_concat[["Date", "Price", "Type"]]],
            ignore_index=True,
        )

        future_pred_df.loc[future_pred_df.index.min(), ["lower", "upper"]] = last_actual_price  # Set start of band
        future_uncertainty_df = pd.concat(
            [
                pd.DataFrame([{"Date": last_actual_date, "lower": last_actual_price, "upper": last_actual_price}]),
                future_pred_df[["Date", "lower", "upper"]],
            ],
            ignore_index=True,
        )

    lines_to_concat = [hist_df, future_pred_df_for_concat if "future_pred_df_for_concat" in locals() else future_pred_df]
    lines_df = pd.concat(lines_to_concat, ignore_index=True).dropna(subset=["Date", "Price"])

    # --- Prepare Shaded Region Data ---
    rect_df_list = []
    if not eval_dates.empty:
        rect_df_list.append(
            pd.DataFrame([{"xmin": eval_dates.min(), "xmax": eval_dates.max(), "Period": "Evaluation Period"}])
        )
    if not prediction_input_dates.empty:
        rect_df_list.append(
            pd.DataFrame(
                [{"xmin": prediction_input_dates.min(), "xmax": prediction_input_dates.max(), "Period": "Prediction Input"}]
            )
        )

    if rect_df_list:
        rect_df = pd.concat(rect_df_list, ignore_index=True)
    else:
        rect_df = pd.DataFrame()

    # --- Timezone & Data Type Normalization ---
    target_tz = "America/New_York"
    lines_df["Date"] = pd.to_datetime(lines_df["Date"], errors="coerce", utc=True).dt.tz_convert(target_tz)
    if not rect_df.empty:
        rect_df["xmin"] = pd.to_datetime(rect_df["xmin"], errors="coerce", utc=True).dt.tz_convert(target_tz)
        rect_df["xmax"] = pd.to_datetime(rect_df["xmax"], errors="coerce", utc=True).dt.tz_convert(target_tz)

    # --- Timezone & Data Type Normalization for uncertainty band ---
    if "future_uncertainty_df" in locals():
        future_uncertainty_df["Date"] = pd.to_datetime(future_uncertainty_df["Date"], errors="coerce", utc=True).dt.tz_convert(
            target_tz
        )

    # --- Determine Plot Date Range ---
    plot_start_date = None
    if not eval_dates.empty:
        min_eval_date = eval_dates.min()
        # Zoom in a bit more on recent history by showing fewer historical days
        plot_start_date = min_eval_date - pd.Timedelta(days=LOOK_BACK + 60)
    elif not lines_df.empty:
        plot_start_date = lines_df["Date"].max() - pd.Timedelta(days=365)

    if plot_start_date:
        # Filter data to the new plot range
        lines_df = lines_df[lines_df["Date"] >= plot_start_date]
        if "future_uncertainty_df" in locals():
            future_uncertainty_df = future_uncertainty_df[future_uncertainty_df["Date"] >= plot_start_date]

    # --- Dynamic X-axis breaks for better readability ---
    # Get the actual date range of the data being plotted
    actual_plot_start_date = lines_df["Date"].min()
    plot_end_date = future_dates.max() if not future_dates.empty else lines_df["Date"].max()
    total_days = (plot_end_date - actual_plot_start_date).days

    if total_days > 150:  # approx 5 months
        # For longer periods, show more breaks in the recent past (last 3 months)
        breaks = []
        recent_part_start = plot_end_date - pd.Timedelta(days=90)

        # Monthly breaks for the older part
        older_part_end = recent_part_start - pd.Timedelta(days=1)
        if actual_plot_start_date < older_part_end:
            breaks.extend(pd.date_range(start=actual_plot_start_date, end=older_part_end, freq="MS"))  # Month Start

        # Bi-weekly breaks for the recent part
        breaks.extend(pd.date_range(start=recent_part_start, end=plot_end_date, freq="2W"))  # Every 2 weeks

        x_axis_scale = scale_x_datetime(labels=date_format("%Y-%m-%d", tz=target_tz), breaks=sorted(list(set(breaks))))
    elif total_days > 60:  # 2-5 months
        x_axis_scale = scale_x_datetime(labels=date_format("%Y-%m-%d", tz=target_tz), breaks=date_breaks("2 weeks"))
    elif total_days > 20:  # 3 weeks to 2 months
        x_axis_scale = scale_x_datetime(labels=date_format("%Y-%m-%d", tz=target_tz), breaks=date_breaks("1 week"))
    else:  # less than 3 weeks
        x_axis_scale = scale_x_datetime(labels=date_format("%Y-%m-%d", tz=target_tz), breaks=date_breaks("2 days"))

    plot = (
        ggplot()
        # Add shaded regions first to be in the background
        + geom_rect(
            mapping=aes(xmin="xmin", xmax="xmax", ymin=float("-inf"), ymax=float("inf"), fill="Period"),
            data=rect_df,
            alpha=0.2,
            inherit_aes=False,
        )
    )

    # Add uncertainty band if available
    if "future_uncertainty_df" in locals() and not future_uncertainty_df.empty:
        plot += geom_ribbon(
            mapping=aes(x="Date", ymin="lower", ymax="upper"),
            data=future_uncertainty_df,
            fill=uncertainty_fill_color,
            alpha=0.2,
            inherit_aes=False,
        )

    # Add lines, scales, and labels to the plot
    plot += geom_line(
        mapping=aes(x="Date", y="Price", color="Type", linetype="Type"),
        data=lines_df,
        na_rm=True,
        size=0.75,  # Slightly thicker line for better visibility
    )
    plot += scale_color_manual(name="Data", values=color_map)
    plot += scale_linetype_manual(name="Data", values=linetype_map)
    plot += scale_fill_manual(name="Periods", values=region_color_map)
    plot += labs(x="Date", y="Price (USD)")
    plot += x_axis_scale
    plot += theme_bw()
    plot += theme(legend_title=element_text(size=10, face="bold"), legend_key_size=15)

    img_buffer = io.BytesIO()
    try:
        plot.save(img_buffer, format="png", dpi=300, width=12, height=6, verbose=False)
        img_buffer.seek(0)
        img_base64 = base64.b64encode(img_buffer.read()).decode("utf-8")
        logger.info(f"[{ticker}] Redesigned prediction plot generated successfully.")
        return img_base64
    except Exception as e:
        logger.error(f"[{ticker}] Error generating redesigned plot image: {e}", exc_info=True)
        return None
    finally:
        img_buffer.close()

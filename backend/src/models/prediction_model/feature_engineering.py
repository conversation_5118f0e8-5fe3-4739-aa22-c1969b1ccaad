import warnings  # Import warnings module

import numpy as np
import pandas as pd
import pandas_ta as ta
import yfinance as yf
from sklearn.preprocessing import StandardScaler
from yahooquery import Ticker

from ...utils.logger import get_logger
from ...utils.technical_indicators import (
    calculate_adx,
    calculate_atr,
    calculate_bollinger_bands,
    calculate_macd,
    calculate_roc,
    calculate_sma,
    calculate_supersmoother,
    calculate_wilder_rsi,
)

logger = get_logger()


def prepare_data_for_transformer(data, look_back, target_column_index, future_prediction_days):
    """
    Prepares data into sequences for the Transformer model.
    Args:
        data (np.array): Scaled data array (can be multi-feature).
        look_back (int): Number of previous time steps to use as input variables.
        target_column_index (int): The index of the target variable column in the data array.
        future_prediction_days (int): The number of future steps to predict.
    Returns:
        X_np (np.array): Input sequences.
        y_np (np.array): Target sequences.
    """
    X, y = [], []
    end_of_data_idx = len(data) - look_back - future_prediction_days + 1

    for i in range(end_of_data_idx):
        # Input sequence uses all features up to time t-1
        X.append(data[i : (i + look_back), :])
        # Target is a sequence of future values from the target column
        y.append(data[i + look_back : i + look_back + future_prediction_days, target_column_index])

    if not X or not y:
        logger.warning("Data preparation resulted in empty X or y. Check data length and look_back period.")
        return np.array([]), np.array([])

    X_np, y_np = np.array(X), np.array(y)
    # y_np shape will be (n_samples, future_prediction_days)
    return X_np, y_np


def preprocess_stock_data(
    stock_data_full, look_back, eval_period_days, ticker, future_prediction_days, ticker_for_log="stock"
):
    """
    Calculates features, scales the data (fitting scaler on train only),
    splits it into training and evaluation sets, and prepares it for the Transformer model,
    predicting price returns.
    Args:
        stock_data_full (pd.DataFrame): DataFrame of stock full data open, close, high, low, volume.
        look_back (int): Look-back period for sequences.
        eval_period_days (int): Number of days for the evaluation set.
        ticker (str): The stock ticker symbol (e.g., 'AAPL') for fetching yfinance data.
        future_prediction_days (int): Number of future days to predict for the target sequence.
        ticker_for_log (str): Ticker symbol for logging purposes.
    Returns:
        tuple: (scaler, X_train, y_train, X_eval, y_eval_actual_scaled,
                close_column_index, target_column_index,
                original_complete_dates_for_plotting, original_complete_close_prices_for_plotting,
                processed_dates_index, # Dates after dropna, for eval and future start
                last_sequence_scaled,
                feature_names,
                train_split_idx)
               or (None, None, None, None, None, None, None, None, None, None, None, None, None) if data is insufficient.
    """
    original_complete_dates_for_plotting = stock_data_full.index.copy()
    original_complete_close_prices_for_plotting = stock_data_full["Close"].copy().values

    # Convert to pandas Series for easier feature calculation
    close_prices_series = stock_data_full["Close"].rename("Close")
    high_prices_series = stock_data_full["High"].rename("High")
    low_prices_series = stock_data_full["Low"].rename("Low")
    open_prices_series = stock_data_full["Open"].rename("Open")
    volume_series = stock_data_full["Volume"].rename("Volume").astype(np.float64)

    # --- Calculate Target Variable: Price Returns ---
    target_returns = close_prices_series.pct_change()
    target_returns.name = "TargetReturn"  # Explicitly name the series

    # --- Initialize yfinance ticker for fundamental data ---
    yf_ticker = None
    ticker_info = {}
    try:
        yf_ticker = yf.Ticker(ticker)
        ticker_info = yf_ticker.info
        if not ticker_info:
            logger.warning(
                f"[{ticker_for_log}] yf.Ticker('{ticker}').info returned empty dictionary. Fundamental data will be NaN."
            )
            ticker_info = {}  # Ensure it's a dict to avoid errors with .get()
    except Exception as e:
        logger.warning(f"[{ticker_for_log}] Could not fetch yfinance info for {ticker}: {e}. Fundamental data will be NaN.")
        ticker_info = {}  # Ensure it's a dict

    # --- Fundamental Metrics ---
    mc_val = ticker_info.get("marketCap", np.nan)
    market_cap = pd.Series(mc_val, index=close_prices_series.index, name="MarketCap").astype(np.float64)
    trailing_pe_val = ticker_info.get("trailingPE", np.nan)
    trailing_pe = pd.Series(trailing_pe_val, index=close_prices_series.index, name="TrailingPE")
    forward_pe_val = ticker_info.get("forwardPE", np.nan)
    forward_pe = pd.Series(forward_pe_val, index=close_prices_series.index, name="ForwardPE")
    dividend_yield_val = ticker_info.get("dividendYield", np.nan)
    dividend_yield = None
    if pd.notna(dividend_yield_val):
        dividend_yield = pd.Series(dividend_yield_val, index=close_prices_series.index, name="DividendYieldStatic")
    profit_margin_val = ticker_info.get("profitMargins", np.nan)
    profit_margin = pd.Series(profit_margin_val, index=close_prices_series.index, name="ProfitMargin")
    roe_val = ticker_info.get("returnOnEquity", np.nan)
    return_on_equity = pd.Series(roe_val, index=close_prices_series.index, name="ROE")
    debt_to_equity_val = ticker_info.get("debtToEquity", np.nan)
    debt_to_equity = pd.Series(debt_to_equity_val, index=close_prices_series.index, name="DebtToEquity")
    eps_val = ticker_info.get("trailingEps", np.nan)
    earnings_per_share = pd.Series(eps_val, index=close_prices_series.index, name="EPS")
    pb_ratio_val = ticker_info.get("priceToBook", np.nan)
    price_to_book = pd.Series(pb_ratio_val, index=close_prices_series.index, name="PriceToBook")
    recommendation_mean_val = ticker_info.get("recommendationMean", np.nan)
    recommendation_mean = pd.Series(recommendation_mean_val, index=close_prices_series.index, name="RecommendationMean")
    gross_profit_margin_val = np.nan
    try:
        quarterly_is = yf_ticker.quarterly_income_stmt
        if quarterly_is is not None and not quarterly_is.empty:
            latest_statement = quarterly_is.iloc[:, 0]
            gross_profit_key_found = next(
                (key for key in ["GrossProfit", "grossProfit", "Gross Profit"] if key in latest_statement.index), None
            )
            total_revenue_key_found = next(
                (key for key in ["TotalRevenue", "totalRevenue", "Total Revenue"] if key in latest_statement.index), None
            )
            if gross_profit_key_found and total_revenue_key_found:
                gross_profit = latest_statement[gross_profit_key_found]
                total_revenue = latest_statement[total_revenue_key_found]
                if pd.notna(total_revenue) and pd.notna(gross_profit) and total_revenue != 0:
                    gross_profit_margin_val = gross_profit / total_revenue
    except Exception as e:
        logger.warning(f"[{ticker_for_log}] Could not fetch or process quarterly income statement for GPM: {e}")
    gross_profit_margin = pd.Series(gross_profit_margin_val, index=close_prices_series.index, name="GrossProfitMargin")

    # Calculate features
    sma_window = 20
    bb_window = 20
    macd_short_window = 12
    macd_long_window = 26
    macd_signal_window = 9
    roc_window = 12
    atr_window = 14
    sma = calculate_sma(close_prices_series, sma_window)
    rsi = calculate_wilder_rsi(close_prices_series)
    bb_middle, bb_upper, bb_lower = calculate_bollinger_bands(close_prices_series, bb_window)
    macd_df, _, _, _ = calculate_macd(close_prices_series, macd_short_window, macd_long_window, macd_signal_window)
    macd_line = macd_df["MACD"]
    macd_signal = macd_df["Signal_Line"]
    macd_hist = macd_df["MACD_Histogram"]
    roc = calculate_roc(close_prices_series, roc_window)
    atr_values = calculate_atr(high_prices_series, low_prices_series, close_prices_series, atr_window)
    obv = ta.obv(close_prices_series, volume_series)
    stoch_result = ta.stoch(high_prices_series, low_prices_series, close_prices_series)
    is_valid_stoch_df = isinstance(stoch_result, pd.DataFrame) and not stoch_result.empty
    stoch_k = (
        stoch_result.iloc[:, 0]
        if is_valid_stoch_df and stoch_result.shape[1] > 0
        else pd.Series(index=close_prices_series.index, dtype=np.float64)
    )
    stoch_d = (
        stoch_result.iloc[:, 1]
        if is_valid_stoch_df and stoch_result.shape[1] > 1
        else pd.Series(index=close_prices_series.index, dtype=np.float64)
    )
    cci = ta.cci(high_prices_series, low_prices_series, close_prices_series, length=20)
    willr = ta.willr(high_prices_series, low_prices_series, close_prices_series, length=14)
    ema20 = ta.ema(close_prices_series, length=20)
    cmo = ta.cmo(close_prices_series, length=14)
    typical_price = (high_prices_series + low_prices_series + close_prices_series) / 3
    tp_volume = typical_price * volume_series
    vwap = tp_volume.cumsum() / volume_series.cumsum()
    vwap.name = "VWAP"
    ss_oscillator = calculate_supersmoother(close_prices_series, 10)
    ss_oscillator.name = "SS_Oscillator"

    kc_result = ta.kc(high_prices_series, low_prices_series, close_prices_series, length=20)
    is_valid_kc_df = isinstance(kc_result, pd.DataFrame) and not kc_result.empty
    kc_lower = (
        kc_result.iloc[:, 0]
        if is_valid_kc_df and kc_result.shape[1] > 0
        else pd.Series(index=close_prices_series.index, dtype=np.float64)
    )
    kc_middle = (
        kc_result.iloc[:, 1]
        if is_valid_kc_df and kc_result.shape[1] > 1
        else pd.Series(index=close_prices_series.index, dtype=np.float64)
    )
    kc_upper = (
        kc_result.iloc[:, 2]
        if is_valid_kc_df and kc_result.shape[1] > 2
        else pd.Series(index=close_prices_series.index, dtype=np.float64)
    )
    raw_ichimoku_output = ta.ichimoku(high_prices_series, low_prices_series, close_prices_series)
    potential_ichimoku_df = (
        raw_ichimoku_output[0]
        if isinstance(raw_ichimoku_output, tuple) and len(raw_ichimoku_output) > 0
        else raw_ichimoku_output
    )
    ichimoku_tenkan = (
        potential_ichimoku_df.iloc[:, 0]
        if isinstance(potential_ichimoku_df, pd.DataFrame) and "ISA_9" in potential_ichimoku_df.columns
        else pd.Series(index=close_prices_series.index, dtype=np.float64)
    )
    ichimoku_kijun = (
        potential_ichimoku_df.iloc[:, 1]
        if isinstance(potential_ichimoku_df, pd.DataFrame) and "ISB_26" in potential_ichimoku_df.columns
        else pd.Series(index=close_prices_series.index, dtype=np.float64)
    )
    ichimoku_span_a = (
        potential_ichimoku_df.iloc[:, 2]
        if isinstance(potential_ichimoku_df, pd.DataFrame) and "ITS_9" in potential_ichimoku_df.columns
        else pd.Series(index=close_prices_series.index, dtype=np.float64)
    )
    ichimoku_span_b = (
        potential_ichimoku_df.iloc[:, 3]
        if isinstance(potential_ichimoku_df, pd.DataFrame) and "IKS_26" in potential_ichimoku_df.columns
        else pd.Series(index=close_prices_series.index, dtype=np.float64)
    )
    ichimoku_chikou = (
        potential_ichimoku_df.iloc[:, 4]
        if isinstance(potential_ichimoku_df, pd.DataFrame) and "ICS_26" in potential_ichimoku_df.columns
        else pd.Series(index=close_prices_series.index, dtype=np.float64)
    )
    adl = ta.ad(high_prices_series, low_prices_series, close_prices_series, volume_series)

    # --- Option-based features ---
    option_features = get_ticker_option_chain_features(ticker, ticker_for_log)
    opt_total_call_vol = pd.Series(option_features["OptTotalCallVol"], index=close_prices_series.index, name="OptTotalCallVol")
    opt_total_put_vol = pd.Series(option_features["OptTotalPutVol"], index=close_prices_series.index, name="OptTotalPutVol")
    opt_put_call_vol_ratio = pd.Series(
        option_features["OptPutCallVolRatio"], index=close_prices_series.index, name="OptPutCallVolRatio"
    )
    opt_total_call_oi = pd.Series(option_features["OptTotalCallOI"], index=close_prices_series.index, name="OptTotalCallOI")
    opt_total_put_oi = pd.Series(option_features["OptTotalPutOI"], index=close_prices_series.index, name="OptTotalPutOI")
    opt_put_call_oi_ratio = pd.Series(
        option_features["OptPutCallOIRatio"], index=close_prices_series.index, name="OptPutCallOIRatio"
    )
    opt_wavg_call_iv = pd.Series(option_features["OptWAvgCallIV"], index=close_prices_series.index, name="OptWAvgCallIV")
    opt_wavg_put_iv = pd.Series(option_features["OptWAvgPutIV"], index=close_prices_series.index, name="OptWAvgPutIV")

    features_dict = {
        "Close": close_prices_series,
        "High": high_prices_series,
        "Low": low_prices_series,
        "Open": open_prices_series,
        "Volume": volume_series,
        "TargetReturn": target_returns,
        "MarketCap": market_cap,
        "TrailingPE": trailing_pe,
        "ForwardPE": forward_pe,
        "ProfitMargin": profit_margin,
        "ROE": return_on_equity,
        "DebtToEquity": debt_to_equity,
        "EPS": earnings_per_share,
        "PriceToBook": price_to_book,
        "RecommendationMean": recommendation_mean,
        "GrossProfitMargin": gross_profit_margin,
        "SMA": sma,
        "RSI": rsi,
        "BB_Middle": bb_middle,
        "BB_Upper": bb_upper,
        "BB_Lower": bb_lower,
        "MACD_Line": macd_line,
        "MACD_Signal": macd_signal,
        "ROC": roc,
        "ATR": atr_values,
        "OBV": obv,
        "STOCH_k": stoch_k,
        "STOCH_d": stoch_d,
        "CCI": cci,
        "WILLR": willr,
        "EMA20": ema20,
        "CMO": cmo,
        "VWAP": vwap,
        "SS_Oscillator": ss_oscillator,
        "KC_Lower": kc_lower,
        "KC_Middle": kc_middle,
        "KC_Upper": kc_upper,
        "ICHIMOKU_TENKAN": ichimoku_tenkan,
        "ICHIMOKU_KIJUN": ichimoku_kijun,
        "ICHIMOKU_SPAN_A": ichimoku_span_a,
        "ICHIMOKU_SPAN_B": ichimoku_span_b,
        "ICHIMOKU_CHIKOU": ichimoku_chikou,
        "ADL": adl,
        "MACD_Hist": macd_hist,
        "OptTotalCallVol": opt_total_call_vol,
        "OptTotalPutVol": opt_total_put_vol,
        "OptPutCallVolRatio": opt_put_call_vol_ratio,
        "OptTotalCallOI": opt_total_call_oi,
        "OptTotalPutOI": opt_total_put_oi,
        "OptPutCallOIRatio": opt_put_call_oi_ratio,
        "OptWAvgCallIV": opt_wavg_call_iv,
        "OptWAvgPutIV": opt_wavg_put_iv,
    }
    if dividend_yield is not None:
        features_dict["DividendYieldStatic"] = dividend_yield

    # Create DataFrame with proper column handling for edge cases
    try:
        data_df = pd.concat(features_dict.values(), axis=1)
        # Only set column names if the number of columns matches
        if len(data_df.columns) == len(features_dict.keys()):
            data_df.columns = list(features_dict.keys())
        else:
            logger.warning(
                f"Column count mismatch: DataFrame has {len(data_df.columns)} columns, "
                f"features_dict has {len(features_dict.keys())} keys. Using default column names."
            )
    except Exception as e:
        logger.error(f"Error creating DataFrame from features: {e}")
        return (None,) * 14  # Return 14 None values to match expected return tuple length

    warnings.simplefilter("ignore", UserWarning)
    data_df.replace([np.inf, -np.inf], np.nan, inplace=True)

    # Create feature availability mask before any imputation
    feature_availability_mask = ~data_df.isnull()

    # Log feature availability statistics
    availability_stats = feature_availability_mask.mean()
    logger.info(f"[{ticker_for_log}] Feature availability statistics:")
    for feature, availability in availability_stats.items():
        if availability < 0.9:  # Log features with less than 90% availability
            logger.info(f"  {feature}: {availability:.2%} available")

    # Intelligent missing data handling by feature type
    # 1. Price-based features: forward fill (prices are continuous)
    price_features = ['Close', 'High', 'Low', 'Open', 'Volume', 'TargetReturn']
    for feature in price_features:
        if feature in data_df.columns:
            data_df[feature].fillna(method='ffill', inplace=True)

    # 2. Technical indicators: forward fill then backward fill
    technical_features = [col for col in data_df.columns if any(x in col for x in
                         ['SMA', 'RSI', 'BB_', 'MACD', 'ROC', 'ATR', 'OBV', 'STOCH', 'CCI', 'WILLR', 'EMA', 'CMO', 'VWAP', 'KC_', 'ICHIMOKU', 'ADL'])]
    for feature in technical_features:
        data_df[feature].fillna(method='ffill', inplace=True)
        data_df[feature].fillna(method='bfill', inplace=True)

    # 3. Fundamental features: forward fill (fundamentals change slowly)
    fundamental_features = [col for col in data_df.columns if any(x in col for x in
                           ['MarketCap', 'PE', 'ProfitMargin', 'ROE', 'Debt', 'EPS', 'PriceBook', 'Recommendation', 'Dividend'])]
    for feature in fundamental_features:
        data_df[feature].fillna(method='ffill', inplace=True)
        # For fundamentals, use median imputation if still missing
        if data_df[feature].isnull().any():
            median_val = data_df[feature].median()
            if not pd.isna(median_val):
                data_df[feature].fillna(median_val, inplace=True)

    # 4. Options features: forward fill then use zero (no options activity)
    options_features = [col for col in data_df.columns if 'Opt' in col]
    for feature in options_features:
        data_df[feature].fillna(method='ffill', inplace=True)
        data_df[feature].fillna(0, inplace=True)  # No options activity

    # Final check: drop rows only if critical features are missing
    critical_features = ['Close', 'TargetReturn']
    critical_missing = data_df[critical_features].isnull().any(axis=1)

    if critical_missing.any():
        logger.warning(f"[{ticker_for_log}] Dropping {critical_missing.sum()} rows with missing critical features")
        data_df = data_df[~critical_missing]
        feature_availability_mask = feature_availability_mask[~critical_missing]

    feature_length = len(data_df.columns)

    if data_df.empty:
        logger.error(f"[{ticker_for_log}] DataFrame is empty after processing missing data.")
        return (None,) * 17  # Updated to match new return tuple length

    processed_dates_index = data_df.index
    feature_names = data_df.columns.tolist()
    close_column_index = feature_names.index("Close")
    target_column_index = feature_names.index("TargetReturn")

    scaler = StandardScaler()
    data_scaled = scaler.fit_transform(data_df)

    train_split_idx = len(data_scaled) - eval_period_days
    if train_split_idx < look_back:
        logger.error(
            f"Not enough data to form a single training sequence. train_split_idx ({train_split_idx}) < look_back ({look_back})"
        )
        return (None,) * 17  # Return 17 None values to match expected return tuple length

    train_data_scaled = data_scaled[:train_split_idx]
    eval_input_data_scaled = data_scaled[train_split_idx - look_back :]
    last_sequence_scaled = data_scaled[-look_back:]

    X_train, y_train = prepare_data_for_transformer(train_data_scaled, look_back, target_column_index, future_prediction_days)
    if X_train.size == 0:
        logger.error(f"[{ticker_for_log}] Training data preparation resulted in empty X_train.")
        return (None,) * 17  # Return 17 None values to match expected return tuple length

    X_eval, y_eval_actual_scaled = prepare_data_for_transformer(
        eval_input_data_scaled, look_back, target_column_index, future_prediction_days
    )

    scaler.n_features_in_ = data_scaled.shape[1]

    # Create feature availability masks for training data
    train_availability_mask = feature_availability_mask.iloc[:train_split_idx].values
    eval_availability_mask = feature_availability_mask.iloc[train_split_idx - look_back:].values

    warnings.simplefilter("always")
    logger.info(f"[{ticker_for_log}] Preprocessing complete. Final scaled data shape: {data_scaled.shape}")
    logger.info(f"[{ticker_for_log}] Feature availability mask shape: {feature_availability_mask.shape}")

    return (
        scaler,
        X_train,
        y_train,
        X_eval,
        y_eval_actual_scaled,
        close_column_index,
        target_column_index,
        original_complete_dates_for_plotting,
        original_complete_close_prices_for_plotting,
        processed_dates_index,
        last_sequence_scaled,
        feature_names,
        train_split_idx,
        feature_length,
        train_availability_mask,
        eval_availability_mask,
    )


def get_ticker_option_chain_features(ticker_symbol_str, ticker_for_log="stock"):
    """
    Fetches and aggregates option chain features for a given ticker symbol using yahooquery.
    Returns a dictionary of aggregated option metrics.
    """
    # Initialize features with NaNs as a fallback
    option_features = {
        "OptTotalCallVol": np.nan,
        "OptTotalPutVol": np.nan,
        "OptPutCallVolRatio": np.nan,
        "OptTotalCallOI": np.nan,
        "OptTotalPutOI": np.nan,
        "OptPutCallOIRatio": np.nan,
        "OptWAvgCallIV": np.nan,
        "OptWAvgPutIV": np.nan,  # Weighted Average Implied Volatility
    }
    try:
        # As per user's latest diff, Ticker is called without async/validate arguments here.
        # For stability, consider Ticker(ticker_symbol_str, asynchronous=False, validate=True) if issues persist.
        tq_ticker = Ticker(ticker_symbol_str)

        if not hasattr(tq_ticker, "option_chain"):
            logger.warning(
                f"[{ticker_for_log}] Ticker object for {ticker_symbol_str} does not have 'option_chain' attribute. No option data to process. Returning NaNs."
            )
            return option_features

        # .option_chain attribute is expected to return a pandas DataFrame
        # containing all option data (calls and puts) for all expirations.
        options_df = tq_ticker.option_chain

        if options_df is None or options_df.empty:
            logger.info(
                f"[{ticker_for_log}] No option chain data returned for {ticker_symbol_str} (DataFrame is None or empty). Returning NaNs."
            )
            return option_features

        # Verify DataFrame structure: expecting a MultiIndex with option type at the third level.
        if not (isinstance(options_df.index, pd.MultiIndex) and options_df.index.nlevels >= 3):
            logger.warning(
                f"[{ticker_for_log}] Option chain DataFrame for {ticker_symbol_str} does not have the expected MultiIndex structure (found index: {options_df.index}). Cannot reliably parse calls/puts. Returning NaNs."
            )
            return option_features

        # Assuming the third level of the index (index=2) indicates option type ('calls' or 'puts')
        try:
            option_types_level = options_df.index.get_level_values(2).astype(str).str.lower()
        except IndexError:
            logger.warning(
                f"[{ticker_for_log}] Could not access the third level of the MultiIndex for option types in {ticker_symbol_str}. Returning NaNs."
            )
            return option_features

        calls_df = options_df[option_types_level == "calls"]
        puts_df = options_df[option_types_level == "puts"]

        total_call_volume = 0.0
        total_put_volume = 0.0
        total_call_oi = 0.0
        total_put_oi = 0.0
        weighted_call_iv_numerator = 0.0
        total_call_oi_for_iv_weight = 0.0
        weighted_put_iv_numerator = 0.0
        total_put_oi_for_iv_weight = 0.0

        if not calls_df.empty:
            if "volume" in calls_df.columns:
                total_call_volume = pd.to_numeric(calls_df["volume"], errors="coerce").fillna(0).sum()
            if "openInterest" in calls_df.columns:
                call_oi = pd.to_numeric(calls_df["openInterest"], errors="coerce").fillna(0)
                total_call_oi = call_oi.sum()
                if "impliedVolatility" in calls_df.columns and total_call_oi > 0:
                    call_iv = pd.to_numeric(calls_df["impliedVolatility"], errors="coerce")
                    valid_iv_mask = call_iv.notna() & (call_oi > 0)
                    weighted_call_iv_numerator = (call_iv[valid_iv_mask] * call_oi[valid_iv_mask]).sum()
                    total_call_oi_for_iv_weight = call_oi[valid_iv_mask].sum()
        else:
            logger.info(f"[{ticker_for_log}] No call options found in the option chain for {ticker_symbol_str}.")

        if not puts_df.empty:
            if "volume" in puts_df.columns:
                total_put_volume = pd.to_numeric(puts_df["volume"], errors="coerce").fillna(0).sum()
            if "openInterest" in puts_df.columns:
                put_oi = pd.to_numeric(puts_df["openInterest"], errors="coerce").fillna(0)
                total_put_oi = put_oi.sum()
                if "impliedVolatility" in puts_df.columns and total_put_oi > 0:
                    put_iv = pd.to_numeric(puts_df["impliedVolatility"], errors="coerce")
                    valid_iv_mask = put_iv.notna() & (put_oi > 0)
                    weighted_put_iv_numerator = (put_iv[valid_iv_mask] * put_oi[valid_iv_mask]).sum()
                    total_put_oi_for_iv_weight = put_oi[valid_iv_mask].sum()
        else:
            logger.info(f"[{ticker_for_log}] No put options found in the option chain for {ticker_symbol_str}.")

        option_features["OptTotalCallVol"] = total_call_volume
        option_features["OptTotalPutVol"] = total_put_volume
        option_features["OptPutCallVolRatio"] = total_put_volume / total_call_volume if total_call_volume > 0 else np.nan

        option_features["OptTotalCallOI"] = total_call_oi
        option_features["OptTotalPutOI"] = total_put_oi
        option_features["OptPutCallOIRatio"] = total_put_oi / total_call_oi if total_call_oi > 0 else np.nan

        option_features["OptWAvgCallIV"] = (
            weighted_call_iv_numerator / total_call_oi_for_iv_weight if total_call_oi_for_iv_weight > 0 else np.nan
        )
        option_features["OptWAvgPutIV"] = (
            weighted_put_iv_numerator / total_put_oi_for_iv_weight if total_put_oi_for_iv_weight > 0 else np.nan
        )

        if (
            sum(val for val in [total_call_volume, total_put_volume, total_call_oi, total_put_oi] if pd.notna(val) and val > 0)
            > 0
        ):
            # Pre-format IV strings to handle NaN cases before including in the main f-string
            call_iv_display = (
                f"{option_features['OptWAvgCallIV']:.4f}" if pd.notna(option_features["OptWAvgCallIV"]) else "NaN"
            )
            put_iv_display = f"{option_features['OptWAvgPutIV']:.4f}" if pd.notna(option_features["OptWAvgPutIV"]) else "NaN"
            logger.info(
                f"[{ticker_for_log}] Processed option features for {ticker_symbol_str}: Calls(Vol:{total_call_volume},OI:{total_call_oi},WAvgIV:{call_iv_display}), Puts(Vol:{total_put_volume},OI:{total_put_oi},WAvgIV:{put_iv_display})"
            )
        else:
            logger.info(
                f"[{ticker_for_log}] No significant option volume or open interest data found for {ticker_symbol_str} in the processed DataFrame. All option features will be NaN."
            )

    except AttributeError as ae:
        logger.warning(
            f"[{ticker_for_log}] Attribute error encountered for {ticker_symbol_str} (e.g., 'option_chain' missing or structure unexpected): {ae}. Returning NaNs for option features."
        )
    except ImportError:
        logger.error(
            f"[{ticker_for_log}] yahooquery.Ticker could not be imported. Ensure yahooquery is installed. Returning NaNs for option features."
        )
    except Exception as e:
        logger.error(
            f"[{ticker_for_log}] An unexpected error occurred while fetching/processing option chain DataFrame for {ticker_symbol_str}: {type(e).__name__} - {e}",
            exc_info=True,
        )

    return option_features

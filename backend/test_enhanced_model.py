#!/usr/bin/env python3
"""
Test script for the enhanced transformer model with attention pooling and feature masking.
Tests multiple stocks to verify the model works correctly across different stock types.
"""

import os
import sys
import time
import traceback
from datetime import datetime

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.models.prediction_model.model import run_training_pipeline
from src.utils.logger import get_logger

logger = get_logger()

# Test stocks with different characteristics
TEST_STOCKS = [
    'AAPL',  # Large cap tech stock with full fundamental data
    'TSLA',  # High volatility growth stock
    'TSLL',  # Leveraged ETF (may have limited fundamental data)
    'YANG',  # Inverse China ETF (may have very limited fundamental data)
]

def test_stock_prediction(ticker: str, model_storage_root: str, force_retrain: bool = True):
    """
    Test the enhanced model on a single stock.
    
    Args:
        ticker: Stock symbol to test
        model_storage_root: Directory to store model files
        force_retrain: Whether to force retraining (True for testing)
    
    Returns:
        dict: Results including success status, metrics, and any errors
    """
    logger.info(f"=" * 60)
    logger.info(f"Testing enhanced model on {ticker}")
    logger.info(f"=" * 60)
    
    start_time = time.time()
    result = {
        'ticker': ticker,
        'success': False,
        'error': None,
        'training_time': 0,
        'predictions': None,
        'feature_stats': {},
    }
    
    try:
        # Run the training pipeline with the enhanced model
        predictions_output = run_training_pipeline(
            ticker=ticker,
            model_storage_root=model_storage_root,
            force_retrain=force_retrain
        )
        
        training_time = time.time() - start_time
        result['training_time'] = training_time
        
        if predictions_output and isinstance(predictions_output, dict):
            result['success'] = True
            result['predictions'] = predictions_output
            
            # Extract key metrics
            if 'future_prices' in predictions_output:
                future_prices = predictions_output['future_prices']
                if future_prices is not None and len(future_prices) > 0:
                    logger.info(f"✅ {ticker}: Successfully generated {len(future_prices)} future price predictions")
                    logger.info(f"   Training time: {training_time:.2f} seconds")
                    
                    # Log prediction range
                    if hasattr(future_prices, 'values'):
                        price_values = future_prices.values
                        logger.info(f"   Predicted price range: ${price_values.min():.2f} - ${price_values.max():.2f}")
                else:
                    logger.warning(f"⚠️  {ticker}: No future prices generated")
            
            # Check for feature availability information
            if 'feature_importance' in predictions_output:
                result['feature_stats']['importance_available'] = True
            
            # Check for model metrics
            if 'eval_metrics' in predictions_output:
                metrics = predictions_output['eval_metrics']
                logger.info(f"   Evaluation metrics: {metrics}")
                result['eval_metrics'] = metrics
                
        else:
            result['error'] = "Training pipeline returned empty or invalid result"
            logger.error(f"❌ {ticker}: {result['error']}")
            
    except Exception as e:
        result['error'] = str(e)
        result['traceback'] = traceback.format_exc()
        logger.error(f"❌ {ticker}: Training failed with error: {e}")
        logger.debug(f"Full traceback:\n{result['traceback']}")
    
    return result

def analyze_results(results):
    """Analyze and summarize test results across all stocks."""
    logger.info(f"\n" + "=" * 80)
    logger.info("ENHANCED MODEL TEST SUMMARY")
    logger.info(f"=" * 80)
    
    successful_stocks = [r for r in results if r['success']]
    failed_stocks = [r for r in results if not r['success']]
    
    logger.info(f"Total stocks tested: {len(results)}")
    logger.info(f"Successful: {len(successful_stocks)}")
    logger.info(f"Failed: {len(failed_stocks)}")
    logger.info(f"Success rate: {len(successful_stocks)/len(results)*100:.1f}%")
    
    if successful_stocks:
        avg_training_time = sum(r['training_time'] for r in successful_stocks) / len(successful_stocks)
        logger.info(f"Average training time: {avg_training_time:.2f} seconds")
        
        logger.info(f"\n✅ SUCCESSFUL STOCKS:")
        for result in successful_stocks:
            ticker = result['ticker']
            time_taken = result['training_time']
            logger.info(f"   {ticker}: {time_taken:.2f}s")
            
            # Check if predictions were generated
            if result['predictions'] and 'future_prices' in result['predictions']:
                future_prices = result['predictions']['future_prices']
                if future_prices is not None and len(future_prices) > 0:
                    logger.info(f"      → Generated {len(future_prices)} predictions")
    
    if failed_stocks:
        logger.info(f"\n❌ FAILED STOCKS:")
        for result in failed_stocks:
            ticker = result['ticker']
            error = result['error']
            logger.info(f"   {ticker}: {error}")
    
    # Feature masking effectiveness analysis
    logger.info(f"\n🔍 FEATURE MASKING ANALYSIS:")
    logger.info("   Enhanced model should handle missing features better than baseline")
    logger.info("   - AAPL: Expected full feature set (fundamentals + technicals + options)")
    logger.info("   - TSLA: Expected full feature set with high volatility")
    logger.info("   - TSLL: Expected limited fundamentals (leveraged ETF)")
    logger.info("   - YANG: Expected very limited fundamentals (inverse ETF)")
    
    return {
        'total_tested': len(results),
        'successful': len(successful_stocks),
        'failed': len(failed_stocks),
        'success_rate': len(successful_stocks)/len(results)*100,
        'avg_training_time': avg_training_time if successful_stocks else 0,
        'results': results
    }

def main():
    """Main test execution function."""
    logger.info("🚀 Starting Enhanced Transformer Model Test")
    logger.info(f"Testing stocks: {', '.join(TEST_STOCKS)}")
    logger.info(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Set up model storage directory
    model_storage_root = os.path.join(os.path.dirname(__file__), "test_model_data")
    os.makedirs(model_storage_root, exist_ok=True)
    
    logger.info(f"Model storage directory: {model_storage_root}")
    
    # Test each stock
    results = []
    total_start_time = time.time()
    
    for i, ticker in enumerate(TEST_STOCKS, 1):
        logger.info(f"\n📊 Testing stock {i}/{len(TEST_STOCKS)}: {ticker}")
        
        result = test_stock_prediction(
            ticker=ticker,
            model_storage_root=model_storage_root,
            force_retrain=True  # Force retrain to test the enhanced model
        )
        
        results.append(result)
        
        # Brief pause between stocks
        if i < len(TEST_STOCKS):
            time.sleep(2)
    
    total_time = time.time() - total_start_time
    
    # Analyze and summarize results
    summary = analyze_results(results)
    
    logger.info(f"\n⏱️  Total test time: {total_time:.2f} seconds")
    logger.info(f"🎯 Enhanced model test completed!")
    
    # Return summary for potential programmatic use
    return summary

if __name__ == "__main__":
    try:
        summary = main()
        
        # Exit with appropriate code
        if summary['success_rate'] >= 75:  # At least 3 out of 4 stocks should work
            logger.info("✅ Test PASSED - Enhanced model working correctly")
            sys.exit(0)
        else:
            logger.error("❌ Test FAILED - Enhanced model needs attention")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("\n⏹️  Test interrupted by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"💥 Test script failed: {e}")
        logger.debug(traceback.format_exc())
        sys.exit(1)

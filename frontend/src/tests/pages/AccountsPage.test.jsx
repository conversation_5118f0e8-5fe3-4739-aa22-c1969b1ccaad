import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import AccountsPage from '../../pages/AccountsPage';

// Mock the components that AccountsPage uses
vi.mock('../../components/common/AllAccountSummary', () => ({
  default: ({ onRefresh, key }) => (
    <div data-testid="all-account-summary" data-key={key}>
      <button onClick={onRefresh} data-testid="trigger-refresh">
        Trigger Refresh
      </button>
      All Account Summary
    </div>
  ),
}));

vi.mock('../../components/layout/NavBar', () => ({
  default: () => <div data-testid="navbar">NavBar</div>,
}));

vi.mock('../../styles/common', () => ({
  GradientTypography: ({ children, ...props }) => (
    <div data-testid="gradient-typography" {...props}>
      {children}
    </div>
  ),
}));

// Mock console.log to test logging
const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

describe('AccountsPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    consoleSpy.mockClear();
  });

  describe('Component Rendering', () => {
    it('renders without crashing', () => {
      render(<AccountsPage />);
      expect(screen.getByTestId('navbar')).toBeInTheDocument();
    });

    it('renders all main components', () => {
      render(<AccountsPage />);

      expect(screen.getByTestId('navbar')).toBeInTheDocument();
      expect(screen.getByTestId('gradient-typography')).toBeInTheDocument();
      expect(screen.getByTestId('all-account-summary')).toBeInTheDocument();
    });

    it('displays correct page title', () => {
      render(<AccountsPage />);

      const title = screen.getByTestId('gradient-typography');
      expect(title).toHaveTextContent('账户管理');
    });

    it('renders components in correct layout structure', () => {
      render(<AccountsPage />);

      // Check that components are wrapped in proper MUI Container
      const container = document.querySelector('.MuiContainer-root');
      expect(container).toBeInTheDocument();
      expect(container).toHaveClass('MuiContainer-maxWidthLg');
    });
  });

  describe('Refresh Functionality', () => {
    it('initializes with refresh key at 0', () => {
      render(<AccountsPage />);

      const accountSummary = screen.getByTestId('all-account-summary');
      expect(accountSummary).toHaveAttribute('data-key', '0');
    });

    it('increments refresh key when handleRefresh is called', () => {
      render(<AccountsPage />);

      const triggerButton = screen.getByTestId('trigger-refresh');
      fireEvent.click(triggerButton);

      const accountSummary = screen.getByTestId('all-account-summary');
      expect(accountSummary).toHaveAttribute('data-key', '1');
    });

    it('logs refresh action when handleRefresh is called', () => {
      render(<AccountsPage />);

      const triggerButton = screen.getByTestId('trigger-refresh');
      fireEvent.click(triggerButton);

      expect(consoleSpy).toHaveBeenCalledWith('AccountsPage: 触发刷新');
    });

    it('handles multiple refresh triggers correctly', () => {
      render(<AccountsPage />);

      const triggerButton = screen.getByTestId('trigger-refresh');
      
      // Trigger refresh multiple times
      fireEvent.click(triggerButton);
      fireEvent.click(triggerButton);
      fireEvent.click(triggerButton);

      const accountSummary = screen.getByTestId('all-account-summary');
      expect(accountSummary).toHaveAttribute('data-key', '3');

      expect(consoleSpy).toHaveBeenCalledTimes(3);
    });
  });

  describe('Component Props', () => {
    it('passes onRefresh prop to AllAccountSummary', () => {
      render(<AccountsPage />);

      const triggerButton = screen.getByTestId('trigger-refresh');
      expect(triggerButton).toBeInTheDocument();

      // Verify that clicking the button works (function was passed correctly)
      fireEvent.click(triggerButton);
      
      const accountSummary = screen.getByTestId('all-account-summary');
      expect(accountSummary).toHaveAttribute('data-key', '1');
    });

    it('passes key prop to AllAccountSummary for re-rendering', () => {
      render(<AccountsPage />);

      const accountSummary = screen.getByTestId('all-account-summary');
      expect(accountSummary).toHaveAttribute('data-key');
      expect(accountSummary.getAttribute('data-key')).toMatch(/^\d+$/);
    });
  });

  describe('Layout and Styling', () => {
    it('applies correct padding to main container', () => {
      render(<AccountsPage />);

      const mainBox = document.querySelector('.MuiBox-root');
      expect(mainBox).toBeInTheDocument();
    });

    it('uses correct container max width', () => {
      render(<AccountsPage />);

      const container = document.querySelector('.MuiContainer-root');
      expect(container).toHaveClass('MuiContainer-maxWidthLg');
    });

    it('applies correct title styling', () => {
      render(<AccountsPage />);

      const title = screen.getByTestId('gradient-typography');
      expect(title).toBeInTheDocument();
    });

    it('applies custom title styles', () => {
      render(<AccountsPage />);

      const title = screen.getByTestId('gradient-typography');

      // Check that custom styles are applied (sx prop should be present)
      expect(title).toHaveAttribute('sx');
    });
  });

  describe('State Management', () => {
    it('maintains independent state for refresh key', () => {
      const { unmount } = render(<AccountsPage />);
      
      const triggerButton = screen.getByTestId('trigger-refresh');
      fireEvent.click(triggerButton);
      
      let accountSummary = screen.getByTestId('all-account-summary');
      expect(accountSummary).toHaveAttribute('data-key', '1');
      
      unmount();
      
      // Re-render component
      render(<AccountsPage />);
      
      accountSummary = screen.getByTestId('all-account-summary');
      expect(accountSummary).toHaveAttribute('data-key', '0');
    });

    it('updates state correctly on consecutive refreshes', () => {
      render(<AccountsPage />);

      const triggerButton = screen.getByTestId('trigger-refresh');
      
      // First refresh
      fireEvent.click(triggerButton);
      let accountSummary = screen.getByTestId('all-account-summary');
      expect(accountSummary).toHaveAttribute('data-key', '1');
      
      // Second refresh
      fireEvent.click(triggerButton);
      accountSummary = screen.getByTestId('all-account-summary');
      expect(accountSummary).toHaveAttribute('data-key', '2');
    });
  });

  describe('Accessibility', () => {
    it('has proper semantic structure', () => {
      render(<AccountsPage />);

      // Should have main content area
      const mainContent = document.querySelector('[data-testid="navbar"] + *');
      expect(mainContent).toBeInTheDocument();
    });

    it('has proper heading hierarchy', () => {
      render(<AccountsPage />);

      const title = screen.getByTestId('gradient-typography');
      expect(title).toHaveAttribute('variant', 'h4');
    });

    it('maintains focus management', () => {
      render(<AccountsPage />);

      const triggerButton = screen.getByTestId('trigger-refresh');
      triggerButton.focus();
      expect(document.activeElement).toBe(triggerButton);
    });
  });

  describe('Animation and Visual Effects', () => {
    it('applies animation styles to title', () => {
      render(<AccountsPage />);

      const title = screen.getByTestId('gradient-typography');

      // The sx prop should contain animation configuration
      expect(title).toHaveAttribute('sx');
    });

    it('centers title text', () => {
      render(<AccountsPage />);

      const title = screen.getByTestId('gradient-typography');

      // Should have text alignment configuration
      expect(title).toHaveAttribute('sx');
    });
  });

  describe('Performance', () => {
    it('does not cause unnecessary re-renders', () => {
      const { rerender } = render(<AccountsPage />);
      
      const initialAccountSummary = screen.getByTestId('all-account-summary');
      const initialKey = initialAccountSummary.getAttribute('data-key');
      
      // Re-render without state change
      rerender(<AccountsPage />);
      
      const rerenderedAccountSummary = screen.getByTestId('all-account-summary');
      expect(rerenderedAccountSummary.getAttribute('data-key')).toBe(initialKey);
    });

    it('only logs when refresh is actually triggered', () => {
      render(<AccountsPage />);

      // Should not log on initial render
      expect(consoleSpy).not.toHaveBeenCalled();

      const triggerButton = screen.getByTestId('trigger-refresh');
      fireEvent.click(triggerButton);

      // Should log only once when triggered
      expect(consoleSpy).toHaveBeenCalledTimes(1);
    });
  });

  describe('Component Integration', () => {
    it('properly integrates with AllAccountSummary component', () => {
      render(<AccountsPage />);

      const accountSummary = screen.getByTestId('all-account-summary');
      const triggerButton = screen.getByTestId('trigger-refresh');

      // Initial state
      expect(accountSummary).toHaveAttribute('data-key', '0');

      // After refresh
      fireEvent.click(triggerButton);
      expect(accountSummary).toHaveAttribute('data-key', '1');
    });

    it('maintains proper component hierarchy', () => {
      render(<AccountsPage />);

      const navbar = screen.getByTestId('navbar');
      const title = screen.getByTestId('gradient-typography');
      const accountSummary = screen.getByTestId('all-account-summary');

      // Check that components exist in expected order
      expect(navbar).toBeInTheDocument();
      expect(title).toBeInTheDocument();
      expect(accountSummary).toBeInTheDocument();
    });
  });
});

name: Deploy to Production

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  build-and-push:
    name: Build and Push Docker Images
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,prefix={{branch}}-

      - name: Build and push Docker image
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.ref == 'refs/heads/main' || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging')
    
    environment:
      name: staging
      url: https://staging.your-app.com
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy to staging
        run: |
          echo "🚀 Deploying to staging environment..."
          echo "Image: ${{ needs.build-and-push.outputs.image-tag }}"
          
          # Example deployment commands:
          # kubectl set image deployment/stock-app backend=${{ needs.build-and-push.outputs.image-tag }}
          # helm upgrade stock-app ./helm-chart --set image.tag=${{ needs.build-and-push.outputs.image-tag }}
          # docker-compose -f docker-compose.staging.yml up -d
          
          echo "✅ Staging deployment completed!"

      - name: Run smoke tests
        run: |
          echo "🧪 Running smoke tests..."
          # Add smoke tests here
          # curl -f https://staging.your-app.com/health
          # npm run test:e2e:staging
          echo "✅ Smoke tests passed!"

      - name: Notify deployment
        uses: 8398a7/action-slack@v3
        if: always()
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          text: |
            Staging deployment ${{ job.status }}!
            Branch: ${{ github.ref_name }}
            Commit: ${{ github.sha }}
            Image: ${{ needs.build-and-push.outputs.image-tag }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build-and-push, deploy-staging]
    if: startsWith(github.ref, 'refs/tags/v') || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production')
    
    environment:
      name: production
      url: https://your-app.com
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Create deployment
        id: deployment
        uses: actions/github-script@v7
        with:
          script: |
            const deployment = await github.rest.repos.createDeployment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: context.sha,
              environment: 'production',
              description: 'Production deployment',
              auto_merge: false,
              required_contexts: []
            });
            return deployment.data.id;

      - name: Deploy to production
        run: |
          echo "🚀 Deploying to production environment..."
          echo "Image: ${{ needs.build-and-push.outputs.image-tag }}"
          
          # Example production deployment commands:
          # kubectl set image deployment/stock-app backend=${{ needs.build-and-push.outputs.image-tag }}
          # helm upgrade stock-app ./helm-chart --set image.tag=${{ needs.build-and-push.outputs.image-tag }}
          # aws ecs update-service --cluster prod --service stock-app --force-new-deployment
          
          echo "✅ Production deployment completed!"

      - name: Update deployment status (success)
        if: success()
        uses: actions/github-script@v7
        with:
          script: |
            await github.rest.repos.createDeploymentStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              deployment_id: ${{ steps.deployment.outputs.result }},
              state: 'success',
              environment_url: 'https://your-app.com',
              description: 'Production deployment successful'
            });

      - name: Update deployment status (failure)
        if: failure()
        uses: actions/github-script@v7
        with:
          script: |
            await github.rest.repos.createDeploymentStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              deployment_id: ${{ steps.deployment.outputs.result }},
              state: 'failure',
              description: 'Production deployment failed'
            });

      - name: Run production health checks
        run: |
          echo "🏥 Running production health checks..."
          # Add health checks here
          # curl -f https://your-app.com/health
          # curl -f https://your-app.com/api/health
          echo "✅ Health checks passed!"

      - name: Notify production deployment
        uses: 8398a7/action-slack@v3
        if: always()
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          text: |
            🚀 Production deployment ${{ job.status }}!
            Tag: ${{ github.ref_name }}
            Commit: ${{ github.sha }}
            Image: ${{ needs.build-and-push.outputs.image-tag }}
            URL: https://your-app.com
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  rollback:
    name: Rollback Production
    runs-on: ubuntu-latest
    if: failure() && needs.deploy-production.result == 'failure'
    needs: [deploy-production]
    
    environment:
      name: production
    
    steps:
      - name: Rollback production deployment
        run: |
          echo "🔄 Rolling back production deployment..."
          # Add rollback commands here
          # kubectl rollout undo deployment/stock-app
          # helm rollback stock-app
          echo "✅ Rollback completed!"

      - name: Notify rollback
        uses: 8398a7/action-slack@v3
        with:
          status: 'warning'
          channel: '#deployments'
          text: |
            ⚠️ Production deployment rolled back!
            Reason: Deployment failure
            Please investigate and redeploy when ready.
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

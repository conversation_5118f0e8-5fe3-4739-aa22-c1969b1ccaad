version: '3.8'

services:
  # Backend service for development
  backend-dev:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=development
      - LOG_ENV=DEVELOPMENT
      - DATABASE_URL=sqlite:///data/stock_trading.db
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./backend:/app
      - ./data:/app/data
      - ./logs:/app/logs
      - ./cache:/app/cache
    depends_on:
      - redis
    restart: unless-stopped
    command: ["python", "app.py"]

  # Frontend service for development
  frontend-dev:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - REACT_APP_API_URL=http://localhost:5000
      - CHOKIDAR_USEPOLLING=true
    depends_on:
      - backend-dev
    restart: unless-stopped

  # Redis for caching
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # Database admin (optional)
  adminer:
    image: adminer
    ports:
      - "8080:8080"
    environment:
      - ADMINER_DEFAULT_SERVER=sqlite
    profiles:
      - admin

volumes:
  redis_data:

networks:
  default:
    name: stock-trading-dev-network
